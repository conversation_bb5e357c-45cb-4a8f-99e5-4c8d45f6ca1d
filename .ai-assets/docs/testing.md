### Backend, nest.js

- Use `apps/api/src/app/auth/auth.spec.ts` as an example
- Always use `ErrorMessages` constants from `@mio/helpers` instead of hardcoded error message strings in tests. For example:

  ```typescript
  // Instead of:
  expect(response.body.message).toBe("PermissionDenied");

  // Do this:
  import { ErrorMessages } from "@mio/helpers";
  // ...
  expect(response.body.message).toBe(ErrorMessages.PermissionDenied);
  ```

  This ensures consistency and makes it easier to update error messages in the future.

- Use the `apiUrls` variable when doing requests to the API, do not forget the `leadSlash` param:

```typescript
import request from "supertest";
import { apiUrls } from "@mio/helpers";

await request(app.getHttpServer()).get(
  buildUrlWithParams(
    apiUrls.playerEvents,
    { playerId: player.id },
    {
      leadSlash: true,
    },
  ),
);
```

- Try to write more end-to-end style tests rather than ones with shallow mocking as it provides more realism. In the recommended example file you can see how we use in-memory mongodb to facilitate that. - we call an endpoint, let the logic go all the way down to the database and then assert the DB entries themselves.

- **Database Cleanup in Tests**

  - Always clean up database state between tests to prevent test pollution
  - Use the shared `clearAllMongoCollections` helper from `apps/api/src/test/db-utils.ts`
  - Example:

    ```typescript
    import { clearAllMongoCollections } from "apps/api/src/test/db-utils";

    afterEach(async () => {
      // Cleans all collections between test cases
      await clearAllMongoCollections(mongoClient);
      jest.resetAllMocks();
    });
    ```

  - The helper performs sequential deletes to avoid race conditions with the shared in-memory Mongo instance
  - This is already set up in the test environment - just import and use it

- Check ./back-end-guide.md for more details about the backend.

### Front end

We use the notion of an `SDKProvider` wrapping all our react components. It passes down functions that the rest of the code can use to fetch or mutate server data. That's the right point for you to apply stubbing or mocking when testing any logic that includes http calls.
