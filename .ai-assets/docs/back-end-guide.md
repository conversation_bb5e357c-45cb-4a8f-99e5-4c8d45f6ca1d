# Back-End Guide

This document outlines the high-level patterns found in the `apps/api` NestJS application.

## High-Level Patterns

1.  **Module-based structure:** The application is organized into modules (e.g., `/apps/api/src/app/auth`, `/apps/api/src/app/coach-users`, `/apps/api/src/app/organization`, `/apps/api/src/app/player`, `/apps/api/src/app/team`). Each module likely encapsulates related controllers, services, and repositories. This promotes modularity and separation of concerns.

2.  **MVC-like pattern (Controller, Service, Repository):**

    - **Controllers:** Handle incoming requests and delegate logic to services (e.g., `/apps/api/src/app/auth/auth.controller.ts`, `/apps/api/src/app/coach-users/users.controller.ts`).
    - **Services:** Contain the business logic and interact with repositories (e.g., `/apps/api/src/app/auth/auth.service.ts`, `/apps/api/src/app/coach-users/users.service.ts`).
    - **Repositories:** Handle data access and interaction with the database (e.g., `/apps/api/src/app/coach-users/users-repository.ts`, `/apps/api/src/app/organization/organization.repository.ts`). We're using Mongodb.

3.  **Authentication and Authorization:** There's a dedicated `auth` module with guards (`/apps/api/src/app/auth/jwt-auth.guard.ts`) and strategies (`/apps/api/src/app/auth/jwt.strategy.ts`), indicating a JWT-based authentication system. The `permissions` module suggests a role-based access control (RBAC) or a similar authorization mechanism.

4.  **Database Interaction:** The presence of repository files (e.g., `/apps/api/src/app/coach-users/users-repository.ts`, `/apps/api/src/app/organization/organization.repository.ts`) and a `database` module (`/apps/api/src/app/database`) indicates interaction with a MongoDB database. The `/apps/api/src/app/assets/in-memory.engine.ts` file is for an in-memory database for testing.

5.  **API Endpoints:** Controllers define the API endpoints for different resources (e.g., `/auth`, `/users`, `/organization`).

6.  **Request Validation and Pipes:** The presence of pipes like `/apps/api/src/app/shared/validation.pipe.ts`, `/apps/api/src/app/organization/slug-param.pipe.ts`, `/apps/api/src/app/shared/dateQuery.pipe.ts`, and `/apps/api/src/app/shared/statsCategory.pipe.ts` suggests that the application uses pipes for request validation, transformation, and parsing. It uses `zod` under the hood and aims to be quite strict wherever possible.

7.  **Error Handling:** An `/apps/api/src/app/assets/exception.filter.ts` file indicates a centralized approach to handling exceptions and returning consistent error responses.

8.  **Shared Modules and Services:** There are shared modules and services (e.g., `/apps/api/src/app/coach-users/coach-users.shared.module.ts`, `/apps/api/src/app/organization/organization.shared.service.ts`) which likely contain common logic or components used across different modules.

9.  **Testing:** The presence of `.spec.ts` files and a `test` directory (`/apps/api/src/test`) indicates that the application has unit and integration tests.

10. **Configuration:** A `config` module (`/apps/api/src/app/config`) suggests a structured approach to managing application configuration.

## MongoDB Collection Naming

When working with MongoDB collections, always use the exported `*Collection` constant from the respective repository file instead of hardcoding collection names. This ensures consistency and makes refactoring easier.

**Example:**

```typescript
// Instead of:
const collection = mongoClient.collection("team-events");

// Do:
import { teamEventsCollection } from "./team-events/instances/team-event.repository";
const collection = mongoClient.collection(teamEventsCollection);
```

This practice helps prevent typos, makes collection names consistent across the codebase, simplifies future renames, and makes it easier to find all usages of a collection.

## Business Rules

### Player Email Constraints

The system has specific business rules around player emails that must be maintained:

1. **Email Requirements by Age:**

   - Players under 13: No own email required, guardian email is mandatory
   - Players 13-17: Own email required, guardian email is mandatory
   - Players 18+: Own email required, no guardian required

2. **Email Validation:**

   - Players and guardians cannot share the same email address
   - At least one valid email must exist (either player.email OR guardian.email)
   - No valid system state exists where both player.email and guardian.email are null/undefined

3. **Reminder System Email Logic:**
   - The reminder system sends emails to both `player.email` AND `guardian.email` when both exist
   - Falls back to whichever email is available when only one exists
   - This ensures maximum notification coverage for all invited players

These constraints are enforced at the entity level in the Player domain model and should be respected in all tests and factory methods.
