# Email Logging System

## Overview

A lightweight system to store email metadata for debugging, support, and operational insights without storing sensitive content or PII.

## What to Store (Metadata Only)

### Identifiers

- `orgId` - Organization context
- `context` - Email type and related entities
  - `type`: `eventReminder | passwordReset | marketing | generic`
  - `eventId`, `teamId` when applicable
- `template` - Name, locale, version (optional)
- `provider` - Currently `resend`
- `providerMessageId` - From send response for correlation

### Envelope Information

- `from`, `replyTo` - Sender details
- `toDomains` - Recipient domains only (e.g., `example.com`)
- `toHashes` - SHA-256 hash of emails with app-level salt
- `bccCount`, `toCount` - Recipient counts
- `subject` - Optionally masked if sensitive

### Status and Timing

- `status` - `queued | sent | failed | delivered | bounced | complained`
- `createdAt`, `sentAt`, `updatedAt`
- `error` - Code/message/raw data if failed
- Transport latency in milliseconds

## Privacy Considerations

- **No email bodies** - Never store actual content
- **No secrets** - No codes, tokens, reset links
- **Hash recipients** - Store domains + hashed emails, not plaintext
- **TTL records** - Auto-expire after 30-90 days
- **Feature flag** - `EMAIL_LOGGING_ENABLED` for easy disable
- **Sampling** - Consider if volume grows large

## Database Schema

```typescript
type EmailContext =
  | { type: "eventReminder"; eventId: string; teamId?: string }
  | { type: "passwordReset" }
  | { type: "marketing" }
  | { type: "generic" };

type EmailRecord = {
  id: string;
  orgId: string;
  context: EmailContext;
  template?: { name: string; locale?: string; version?: string };
  provider: "resend";
  providerMessageId?: string;

  from: string;
  replyTo?: string | string[];
  toDomains: string[];
  toHashes: string[]; // sha256(email + SALT)
  toCount: number;
  bccCount: number;

  subject?: string;

  status: "queued" | "sent" | "failed" | "delivered" | "bounced" | "complained";
  error?: { code?: string; message?: string; raw?: unknown };

  createdAt: Date;
  sentAt?: Date;
  updatedAt: Date;
};
```

## Required Indexes

- `orgId + createdAt` (descending) - Organization email history
- `status` - Filter by delivery status
- `context.type`, `context.eventId` - Filter by email type/event
- `providerMessageId` (unique) - Webhook correlation
- `createdAt` TTL - Auto-cleanup old records

## Implementation Plan

### 1. EmailLogService

```typescript
class EmailLogService {
  createQueued(record: Partial<EmailRecord>): Promise<string>;
  markSent(id: string, providerMessageId: string): Promise<void>;
  markFailed(id: string, error: any): Promise<void>;
  markWebhookStatus(providerMessageId: string, status: string): Promise<void>;
}
```

### 2. Integration Points

- **Before send**: Create queued record with metadata
- **After success**: Update with provider message ID
- **On failure**: Update with error details
- **Webhook endpoint**: Update delivery status from provider

### 3. Usage Example

```typescript
// In EmailService.sendEventReminder
const logId = await emailLog.createQueued({
  orgId: event.organizationId,
  context: { type: "eventReminder", eventId: event.id },
  template: { name: "EventReminderEmail", locale },
  from: fromEmail,
  replyTo,
  toDomains: playerEmails.map(getDomain),
  toHashes: playerEmails.map(hashWithSalt),
  bccCount: playerEmails.length,
  subject,
});

try {
  const res = await this.resend.emails.send(msg);
  await emailLog.markSent(logId, res.data.id);
} catch (err) {
  await emailLog.markFailed(logId, normalizeError(err));
  throw err;
}
```

## Benefits

- **Debugging** - Trace email delivery issues
- **Support** - Answer "did my email send?" questions
- **Monitoring** - Track delivery rates and failures
- **Compliance** - Audit trail without storing content
- **Analytics** - Email engagement patterns by organization

## Future Enhancements

- Dashboard for email metrics
- Alerting on high failure rates
- Integration with monitoring systems
- Export capabilities for compliance

---

_Created: 2025-01-06_
_Status: Future consideration_
