### Feature description

This feature migrates guardian information from an embedded document within the `players` collection to a new, dedicated `guardians` collection. The goal is to improve data integrity, reduce data duplication, and create a more flexible and scalable architecture for managing guardian information.

### User perspective

This is primarily a backend refactoring, so the immediate user impact is minimal. The main perspective is that of the system and its developers:

- As a developer, I want to manage guardian data independently from player data, allowing for future features like a guardian-specific portal.
- As a developer, I want a single source of truth for guardian information to prevent data inconsistencies.
- As a system, we must ensure a seamless transition with no data loss or service interruption.

### Architecture notes

- The migration will be executed in three main phases to minimize risk: 1) Migrate existing data, 2) Transition application logic to the new collection, 3) Clean up the legacy data structure.
- We will leverage the existing `GuardianRepository` which is already in place and handles basic CRUD operations for the `guardians` collection.
- The `PlayerService` already contains the initial "dual-write" logic, which we will build upon and eventually refactor.
- A new `GuardianService` will be introduced to encapsulate all business logic for guardian management, following the existing NestJS module pattern.
- We will use the `migrate-mongo` library for the data migration scripts, consistent with existing migrations in the `/migrations` directory.

### Task list

- [ ] Create a migration script (`migrations/YYYYMMDDHHMMSS-migrate-embedded-guardians.js`) to move existing embedded guardians to the new `guardians` collection.
- [ ] Create a new `GuardianModule`, including a `GuardianService` and `GuardianController`.
- [ ] Refactor `PlayerService` and `PlayerRepository` to use the new `GuardianService` for all guardian-related data retrieval and manipulation, removing reliance on the embedded document.
- [ ] Implement new API endpoints in `GuardianController` for direct guardian management (e.g., `GET /guardians/:id`, `PUT /guardians/:id`).
- [ ] After verifying the new system is stable, create a second migration script (`migrations/YYYYMMDDHHMMSS-remove-embedded-guardians.js`) to remove the now-redundant `guardian` field from the `players` collection.
- [ ] Clean up the `Player` data model and remove the initial dual-write code from `PlayerService`.
- [ ] Write basic unit tests for the new `GuardianService` methods.
- [ ] Run all existing tests, linting, and type checks to ensure no regressions have been introduced.

### Closing notes

- This phased approach ensures that the migration can be performed safely with clear rollback and verification points.
- It is critical to test the migration scripts on a development database before executing them on production.
- Upon completion, we should consider updating the `back-end-guide.md` documentation to reflect the new `guardians` collection and its API.
