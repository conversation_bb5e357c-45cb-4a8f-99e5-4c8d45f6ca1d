# Guardian Email Reminder Testing Plan

## Overview

This plan covers testing the bug fix that ensures the reminder system correctly sends emails to both player emails and guardian emails based on the player's age and email configuration.

## Bug Fix Description

**Issue**: The reminder system was only checking `player.email` and missing cases where young players (< 13) only have guardian emails.

**Fix**: Updated line 73-79 in `reminders.service.ts` to use:

```typescript
const playerEmails = players
  .flatMap((p) => {
    const emails = [];
    if (p.email) emails.push(p.email);
    if (p.guardian?.email) emails.push(p.guardian.email);
    return emails;
  })
  .filter((e): e is Email => !!e);
```

## Business Rules Tested

### Email Requirements by Age:

- **Players under 13**: No own email required, guardian email is mandatory
- **Players 13-17**: Own email required, guardian email is mandatory
- **Players 18+**: Own email required, no guardian required

### Email Priority Logic:

- The system sends emails to BOTH `player.email` AND `guardian.email` when both exist
- Falls back to whichever email is available when only one exists
- At least one valid email must exist for any player in the system

## Factory Method Additions

Added to `/apps/api/src/test/factories/player.ts`:

1. **`createWithGuardianEmailOnly`**: Creates a player (age < 13) with only guardian email
2. **`createWithBothEmails`**: Creates a player (age 13-17) with both player and guardian emails

## Test Cases Implemented

### 1. Guardian Email Only Test

- **Scenario**: Young player (age 10) with only guardian email
- **Expected**: Reminder sent to guardian email
- **Verifies**: System correctly falls back to guardian email when player email is undefined

### 2. Both Emails Test

- **Scenario**: Teen player (age 15) with both emails
- **Expected**: Reminders sent to both player and guardian emails
- **Verifies**: System sends to both emails when both are available

### 3. Mixed Email Types Test

- **Scenario**: Event with young player (guardian email), teen player (both emails), adult player (own email)
- **Expected**: Reminders sent to guardian email, both teen emails, and adult email (4 emails total)
- **Verifies**: System handles multiple email configurations in single event correctly

## Test Structure

### Test File:

`/apps/api/src/app/reminders/player-reminders.spec.ts`

### Integration Test Pattern:

1. Create organization and coach
2. Create team appropriate for player age
3. Create players with specific email configurations using new factory methods
4. Save players and join them to organization
5. Create team event with player invitations
6. Trigger reminder cron job
7. Verify correct emails were sent
8. Verify database was updated correctly

### Assertions:

- `emailMock.sendEventReminder` called with correct email addresses
- Event's `remindersSent` array updated with correct player IDs
- Correct number of reminders sent

## Coverage Summary

| Scenario     | Player Age | Player Email | Guardian Email | Expected Emails        |
| ------------ | ---------- | ------------ | -------------- | ---------------------- |
| Young Player | 10         | ❌           | ✅             | Guardian only          |
| Teen Player  | 15         | ✅           | ✅             | Both Player + Guardian |
| Adult Player | 25         | ✅           | ❌             | Player only            |
| Mixed Event  | Various    | Various      | Various        | All applicable emails  |

## Integration Points Tested

1. **PlayerRepository.getByIds()**: Retrieves players with guardian data
2. **EmailService.sendEventReminder()**: Receives correct email addresses
3. **TeamEvent.Entity.updateRemindersSent()**: Updates event with reminder timestamps
4. **TeamEventRepository.updateEvent()**: Persists reminder updates

## Dependencies

- Player entity with guardian relationship
- Email service mock verification
- Test service with player creation utilities
- Factory methods for various player configurations

## Success Criteria

- ✅ All existing reminder tests continue to pass
- ✅ Guardian-only email reminders work correctly
- ✅ Both player and guardian emails sent when both exist
- ✅ Mixed email configurations in same event handled correctly
- ✅ Database updates track all reminder sends
- ✅ No reminders sent to invalid/missing emails

This comprehensive test suite ensures the guardian email bug fix works correctly across all valid player email configurations while maintaining backward compatibility with existing functionality.
