name: CI/CD

on:
  # Triggers the workflow when a commit is pushed to the `main` branch
  push:
    branches: [main]
  # Triggers the workflow when the PR is opened, re-opened and synchronized (PR head branch is
  # updated) on the `main` branch.
  pull_request:
    branches: [main]

jobs:
  prepare-env:
    runs-on: ubuntu-latest

    steps:
      - name: Prepare environment
        id: prepare-env
        # `base_ref` is only present on `pull_request` events and it's the name of the PR's target
        # branch (the branch we want to merge the PR into - usually it's `main`).
        #
        # `event.before` is the SHA of the most recent commit on ref before the push.
        #
        # The following script set the `base` output of this job to be the the `event.before` SHA if
        # the event is `push`. This means that one or more commits are being pushed (directly or via
        # a PR merge) to the main branch, we make sure that all the Nx `affected` commands are
        # executed against the most recent commit that was made before the changes being pushed.
        #
        # If instead the event is `pull_request`, the `base` output is set to the PR's target branch.
        # This way, when a PR is opened or synchronized, the Nx `affected` commands are always
        # executed against the branch that the PR is targeting.
        run: |
          if [[ ${{ github.event_name }} == push ]]; then
            echo "base=${{ github.event.before }}" >> $GITHUB_OUTPUT
          elif [[ ${{ github.event_name }} == pull_request ]]; then
            echo "base=origin/${{ github.base_ref }}" >> $GITHUB_OUTPUT
          fi

    outputs:
      base: ${{ steps.prepare-env.outputs.base }}

  # This job doesn't do anything: it's here only to run `npm ci` so that the dependencies are cached
  # for all the other jobs.
  cache-dependencies:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          # By default, only the last commit is fetched by GitHub Actions. Nx needs the whole git
          # history to properly figure out the affected projects so we use the `fetch-depth` option
          # to fetch everything.
          # https://stackoverflow.com/a/67201604
          fetch-depth: 0

      - uses: ./.github/actions/setup

  check-formatting:
    runs-on: ubuntu-latest
    needs: [cache-dependencies, prepare-env]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: ./.github/actions/setup

      - name: Check formatting of affected projects
        run: npx nx format:check --base=${{ needs.prepare-env.outputs.base }} --parallel

  lint:
    runs-on: ubuntu-latest
    needs: [cache-dependencies, prepare-env]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: ./.github/actions/setup

      - name: Lint the affected projects
        run: npx nx affected:lint --base=${{ needs.prepare-env.outputs.base }} --parallel

  test:
    runs-on: ubuntu-latest
    needs: [cache-dependencies, prepare-env]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: ./.github/actions/setup

      - name: Test the affected projects
        run: npx nx affected:test --base=${{ needs.prepare-env.outputs.base }} --parallel

  e2e-test:
    runs-on: ubuntu-latest
    needs: [cache-dependencies, prepare-env]
    if: false == true

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: ./.github/actions/setup
        with:
          # Needed to let npm correctly install Cypress
          npm-ignore-scripts: false

      - name: Run e2e tests on affected apps
        run: npx nx affected:e2e --base=${{ needs.prepare-env.outputs.base }}

      - name: Upload Cypress results as artifacts
        uses: actions/upload-artifact@v2
        with:
          name: cypress-tests-result
          path: ./dist/cypress/**/*.*

  type-check:
    runs-on: ubuntu-latest
    needs: [cache-dependencies, prepare-env]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: ./.github/actions/setup

      # Make sure that everything compiles
      - name: Build affected apps
        run: npx nx affected --target=type-check --base=${{ needs.prepare-env.outputs.base }}

  calculate-affected-apps-for-deployment:
    runs-on: ubuntu-latest
    needs: [prepare-env, lint, test, type-check, check-formatting]
    if: ${{ github.event_name == 'push' }}

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: ./.github/actions/setup

      - name: Calculate affected apps
        id: affected-apps
        run: |
          APPS="$(npx nx show projects --affected --base=${{ needs.prepare-env.outputs.base }})"
          NW_APPS="${APPS//$'\n'/ }"
          echo "list=$NW_APPS" >> $GITHUB_OUTPUT

    outputs:
      list: ${{ steps.affected-apps.outputs.list }}
