import { FC } from "react";
import { useTheme } from "@mui/material/styles";

type Props = {
  mainColor?: string;
  secondaryColor?: string;
};

export const OtherEventGraphic: FC<Props> = ({
  mainColor: _mainColor,
  secondaryColor: _secondaryColor,
}) => {
  const theme = useTheme();

  const mainColor = _mainColor || theme.palette.other.blue;
  const secondaryColor = _secondaryColor || theme.palette.grey[600];

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      version="1.1"
      viewBox="195.66420007677283 442.2004428105387 131.68129999999996 131.68129999999996"
      xmlSpace="preserve"
    >
      <g
        transform="matrix(1.842338888 0 0 1.842338888 257.0314675469 508.0410928105)"
        id="6_PIfJwp17U-9LaoK_C-U"
      >
        <path
          style={{
            stroke: secondaryColor,
            strokeWidth: 1,
            strokeDasharray: "none",
            strokeLinecap: "butt",
            strokeDashoffset: 0,
            strokeLinejoin: "miter",
            strokeMiterlimit: 4,
            fill: "none",
          }}
          transform=" translate(0, 0)"
          d="M 0 -32.35294 C 17.85882 -32.35294 32.35294 -17.858819999999994 32.35294 0 C 32.35294 17.85882 17.858819999999994 32.35294 0 32.35294 C -17.85882 32.35294 -32.35294 17.858819999999994 -32.35294 0 C -32.35294 -17.85882 -17.858819999999994 -32.35294 0 -32.35294 z"
          strokeLinecap="round"
        />
      </g>
      <g
        transform="matrix(1.0507576207 0 0 1.0507576207 258.9645529703 477.2872686279)"
        id="rhfHuEJSjR1DMjHuKD4Sm"
      >
        <path
          style={{
            fill: mainColor,
            fillRule: "nonzero",
            opacity: 1,
          }}
          transform=" translate(0, 0)"
          d="M 0 -2.69608 C 1.48824 -2.69608 2.69608 -1.4882399999999998 2.69608 0 C 2.69608 1.48824 1.4882399999999998 2.69608 0 2.69608 C -1.48824 2.69608 -2.69608 1.4882399999999998 -2.69608 0 C -2.69608 -1.48824 -1.4882399999999998 -2.69608 0 -2.69608 z"
          strokeLinecap="round"
        />
      </g>
      <g transform="matrix(0 1 -1 0 293.8315715538 508.0410928105)" id="qLGoA31y70m7Ko1rQGgYo">
        <path
          style={{
            fill: mainColor,
            fillRule: "nonzero",
            opacity: 1,
          }}
          transform=" translate(0, 0)"
          d="M -0.34642 -32.67291 C 32.555589999999995 -32.67291 65.84065000000001 -1.2354200000000013 65.84065000000001 31.666579999999996 C 65.84065000000001 33.60303 59.16616000000001 30.783589999999997 58.98535000000001 32.672909999999995 C 56.09397000000001 2.460929999999994 30.619140000000012 -21.191430000000004 -0.34641999999998774 -21.191430000000004 C -31.311969999999988 -21.191430000000004 -56.78680999999999 2.4609199999999944 -59.67818999999999 32.672909999999995 C -59.85899999999999 30.783589999999993 -65.84064999999998 33.60303 -65.84064999999998 31.666579999999996 C -65.84064999999998 -1.235430000000001 -33.24841999999998 -32.67291 -0.34641999999998063 -32.67291 z"
          strokeLinecap="round"
        />
      </g>
      <g
        transform="matrix(0.4277485084 0 0 0.4277485084 258.649450669 512.6853980127)"
        id="adgpnw8HVXpanjRWlN2F_"
      >
        <path
          style={{
            fill: secondaryColor,
            fillRule: "nonzero",
            opacity: 1,
          }}
          transform=" translate(-13.274325, -28.806915)"
          d="M 26.54866 13.27433 C 26.54866 20.110870000000002 21.37187 25.74566 14.727030000000001 26.46996 L 22.62327 57.61383 L 3.30603 57.276650000000004 L 8.44502 39.61923 L 12.26019 26.510440000000003 C 5.40597 25.991850000000003 -0.000009999999999621423 20.26057 -0.000009999999999621423 13.274330000000003 C -0.000009999999999621423 5.946900000000003 5.946890000000001 1.7763568394002505e-15 13.274320000000001 1.7763568394002505e-15 C 20.601750000000003 1.7763568394002505e-15 26.548650000000002 5.946900000000002 26.548650000000002 13.274330000000003 z"
          strokeLinecap="round"
        />
      </g>
      <g
        transform="matrix(0.4277485084 0 0 0.4277485084 286.3008414382 512.6853980127)"
        id="XBWSsoRG4xAN9K8obTGMK"
      >
        <path
          style={{
            fill: secondaryColor,
            fillRule: "nonzero",
            opacity: 1,
          }}
          transform=" translate(-13.274325, -28.806915)"
          d="M 26.54866 13.27433 C 26.54866 20.110870000000002 21.37187 25.74566 14.727030000000001 26.46996 L 22.62327 57.61383 L 3.30603 57.276650000000004 L 8.44502 39.61923 L 12.26019 26.510440000000003 C 5.40597 25.991850000000003 -0.000009999999999621423 20.26057 -0.000009999999999621423 13.274330000000003 C -0.000009999999999621423 5.946900000000003 5.946890000000001 1.7763568394002505e-15 13.274320000000001 1.7763568394002505e-15 C 20.601750000000003 1.7763568394002505e-15 26.548650000000002 5.946900000000002 26.548650000000002 13.274330000000003 z"
          strokeLinecap="round"
        />
      </g>
      <g
        transform="matrix(0.4277485084 0 0 0.4277485084 230.1800055449 512.6853980127)"
        id="txceziVUWn5dGjC9wD6q8"
      >
        <path
          style={{
            fill: secondaryColor,
            fillRule: "nonzero",
            opacity: 1,
          }}
          transform=" translate(-13.274325, -28.806915)"
          d="M 26.54866 13.27433 C 26.54866 20.110870000000002 21.37187 25.74566 14.727030000000001 26.46996 L 22.62327 57.61383 L 3.30603 57.276650000000004 L 8.44502 39.61923 L 12.26019 26.510440000000003 C 5.40597 25.991850000000003 -0.000009999999999621423 20.26057 -0.000009999999999621423 13.274330000000003 C -0.000009999999999621423 5.946900000000003 5.946890000000001 1.7763568394002505e-15 13.274320000000001 1.7763568394002505e-15 C 20.601750000000003 1.7763568394002505e-15 26.548650000000002 5.946900000000002 26.548650000000002 13.274330000000003 z"
          strokeLinecap="round"
        />
      </g>
    </svg>
  );
};
