import { useTheme } from "@mui/material/styles";
import { FC } from "react";

type Props = {
  mainColor?: string;
  secondaryColor?: string;
};

export const TrainingEventGraphic: FC<Props> = ({
  mainColor: _mainColor,
  secondaryColor: _secondaryColor,
}) => {
  const theme = useTheme();

  const mainColor = _mainColor || theme.palette.secondary.main;
  const secondaryColor = _secondaryColor || theme.palette.grey[600];

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      version="1.1"
      viewBox="189.8088006689881 12.95189688983153 131.68130000000002 131.68130000000002"
      xmlSpace="preserve"
    >
      <g
        transform="matrix(1.842338888 0 0 1.842338888 255.649450669 83.5593934997)"
        id="hMgyBs3Bowx2gX_4-6-wq"
      >
        <path
          style={{
            stroke: secondaryColor,
            strokeWidth: 1,
            strokeDasharray: "none",
            strokeLinecap: "butt",
            strokeDashoffset: 0,
            strokeLinejoin: "miter",
            strokeMiterlimit: 4,
            fill: "none",
            fillRule: "nonzero",
            opacity: 1,
          }}
          transform=" translate(0, 0)"
          d="M 0 -32.35294 C 17.85882 -32.35294 32.35294 -17.858819999999994 32.35294 0 C 32.35294 17.85882 17.858819999999994 32.35294 0 32.35294 C -17.85882 32.35294 -32.35294 17.858819999999994 -32.35294 0 C -32.35294 -17.85882 -17.858819999999994 -32.35294 0 -32.35294 z"
          strokeLinecap="round"
        />
      </g>
      <g
        transform="matrix(0.3822592502 0 0 0.3822592502 278.3314130867 58.9601660693)"
        id="-zA_jNH1fRdv4Xkltwrj0"
      >
        <path
          style={{
            stroke: "none",
            strokeWidth: 1,
            strokeDasharray: "none",
            strokeLinecap: "butt",
            strokeDashoffset: 0,
            strokeLinejoin: "miter",
            strokeMiterlimit: 4,
            fill: secondaryColor,
            fillRule: "nonzero",
            opacity: 1,
          }}
          transform=" translate(-13.27433, -28.86607)"
          d="M 13.27433 0 C 20.60176 0 26.54866 5.9469 26.54866 13.27433 C 26.54866 19.77839 21.86315 25.19475 15.685660000000002 26.32987 L 18.085990000000002 39.634280000000004 L 21.35112 57.73214 L 2.104460000000003 56.04828 L 12.975230000000003 26.54535 C 5.785750000000004 26.38615 3.552713678800501e-15 20.5018 3.552713678800501e-15 13.274329999999999 C 3.552713678800501e-15 5.946899999999999 5.946900000000004 -1.7763568394002505e-15 13.274330000000004 -1.7763568394002505e-15 z"
          strokeLinecap="round"
        />
      </g>
      <g
        transform="matrix(0.3822592502 0 0 0.3822592502 277.3612878056 99.5516920092)"
        id="RVgGxTG_fSa0VXFlozP7X"
      >
        <path
          style={{
            stroke: "none",
            strokeWidth: 1,
            strokeDasharray: "none",
            strokeLinecap: "butt",
            strokeDashoffset: 0,
            strokeLinejoin: "miter",
            strokeMiterlimit: 4,
            fill: secondaryColor,
            fillRule: "nonzero",
            opacity: 1,
          }}
          transform=" translate(-13.27433, -28.86607)"
          d="M 13.27433 0 C 20.60176 0 26.54866 5.9469 26.54866 13.27433 C 26.54866 19.77839 21.86315 25.19475 15.685660000000002 26.32987 L 18.085990000000002 39.634280000000004 L 21.35112 57.73214 L 2.104460000000003 56.04828 L 12.975230000000003 26.54535 C 5.785750000000004 26.38615 3.552713678800501e-15 20.5018 3.552713678800501e-15 13.274329999999999 C 3.552713678800501e-15 5.946899999999999 5.946900000000004 -1.7763568394002505e-15 13.274330000000004 -1.7763568394002505e-15 z"
          strokeLinecap="round"
        />
      </g>
      <g
        transform="matrix(1.0507576207 0 0 1.0507576207 258.3257349477 68.7082590941)"
        id="jYotITsamF2OJRnd7zq1M"
      >
        <path
          style={{
            strokeLinejoin: "miter",
            fill: mainColor,
            fillRule: "nonzero",
          }}
          transform=" translate(0, 0)"
          d="M 0 -2.69608 C 1.48824 -2.69608 2.69608 -1.4882399999999998 2.69608 0 C 2.69608 1.48824 1.4882399999999998 2.69608 0 2.69608 C -1.48824 2.69608 -2.69608 1.4882399999999998 -2.69608 0 C -2.69608 -1.48824 -1.4882399999999998 -2.69608 0 -2.69608 z"
          strokeLinecap="round"
        />
      </g>
      <g transform="matrix(1 0 0 1 255.649450669 46.1723613324)" id="-2EF_lf19CP1D2PxZj24l">
        <path
          style={{
            stroke: secondaryColor,
            strokeWidth: 0,
            strokeDasharray: "none",
            strokeLinecap: "butt",
            strokeDashoffset: 0,
            strokeLinejoin: "miter",
            strokeMiterlimit: 4,
            fill: mainColor,
            fillRule: "nonzero",
            opacity: 1,
          }}
          transform=" translate(0, 0)"
          d="M -0.34642 -32.67291 C 32.555589999999995 -32.67291 65.84065000000001 -1.2354200000000013 65.84065000000001 31.666579999999996 C 65.84065000000001 33.60303 59.16616000000001 30.783589999999997 58.98535000000001 32.672909999999995 C 56.09397000000001 2.460929999999994 30.619140000000012 -21.191430000000004 -0.34641999999998774 -21.191430000000004 C -31.311969999999988 -21.191430000000004 -56.78680999999999 2.4609199999999944 -59.67818999999999 32.672909999999995 C -59.85899999999999 30.783589999999993 -65.84064999999998 33.60303 -65.84064999999998 31.666579999999996 C -65.84064999999998 -1.235430000000001 -33.24841999999998 -32.67291 -0.34641999999998063 -32.67291 z"
          strokeLinecap="round"
        />
      </g>
      <g
        transform="matrix(0.3822592502 0 0 0.3822592502 236.0239506801 58.960164158)"
        id="luyNKl47o4j8uZvB0gWp2"
      >
        <path
          style={{
            stroke: "none",
            strokeWidth: 1,
            strokeDasharray: "none",
            strokeLinecap: "butt",
            strokeDashoffset: 0,
            strokeLinejoin: "miter",
            strokeMiterlimit: 4,
            fill: secondaryColor,
            fillRule: "nonzero",
            opacity: 1,
          }}
          transform=" translate(-13.27434, -28.866075)"
          d="M 26.54866 13.27433 C 26.54866 20.501800000000003 20.76291 26.38615 13.573430000000002 26.54535 L 24.444200000000002 56.048289999999994 L 5.197540000000004 57.73215 L 8.462670000000003 39.63428999999999 L 10.863010000000003 26.329869999999993 C 4.685520000000003 25.194749999999992 0.000010000000003174137 19.778389999999995 0.000010000000003174137 13.274329999999992 C 0.000010000000003174137 5.946899999999992 5.9469100000000035 -8.881784197001252e-15 13.274340000000004 -8.881784197001252e-15 C 20.601770000000002 -8.881784197001252e-15 26.548670000000005 5.946899999999991 26.548670000000005 13.274329999999992 z"
          strokeLinecap="round"
        />
      </g>
      <g
        transform="matrix(0.3822592502 0 0 0.3822592502 236.0239506801 99.5516939205)"
        id="U03JvpR0J86k8hJwIF6Q_"
      >
        <path
          style={{
            stroke: "none",
            strokeWidth: 1,
            strokeDasharray: "none",
            strokeLinecap: "butt",
            strokeDashoffset: 0,
            strokeLinejoin: "miter",
            strokeMiterlimit: 4,
            fill: secondaryColor,
            fillRule: "nonzero",
            opacity: 1,
          }}
          transform=" translate(-13.27434, -28.866075)"
          d="M 26.54866 13.27433 C 26.54866 20.501800000000003 20.76291 26.38615 13.573430000000002 26.54535 L 24.444200000000002 56.048289999999994 L 5.197540000000004 57.73215 L 8.462670000000003 39.63428999999999 L 10.863010000000003 26.329869999999993 C 4.685520000000003 25.194749999999992 0.000010000000003174137 19.778389999999995 0.000010000000003174137 13.274329999999992 C 0.000010000000003174137 5.946899999999992 5.9469100000000035 -8.881784197001252e-15 13.274340000000004 -8.881784197001252e-15 C 20.601770000000002 -8.881784197001252e-15 26.548670000000005 5.946899999999991 26.548670000000005 13.274329999999992 z"
          strokeLinecap="round"
        />
      </g>
    </svg>
  );
};
