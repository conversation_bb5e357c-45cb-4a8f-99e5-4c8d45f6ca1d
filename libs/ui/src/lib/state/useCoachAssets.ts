import { useMutation, useQueryClient } from "@tanstack/react-query";

import { APIError, CustomUrl } from "@mio/helpers";

import { useSdk } from "./useSdk";
import { coachAssets } from "../sdk";

const coachImages = ["coach-images"] as const;

export const useUploadCoachImage = () => {
  const sdk = useSdk();
  const client = useQueryClient();

  return useMutation<CustomUrl.Type, APIError, coachAssets.UploadImageParams>(
    sdk.coach.uploadImage,
    {
      onSuccess: (image) => {
        client.refetchQueries([coachImages]);
      },
    },
  );
};
