import { AxiosInstance } from "axios";
import omit from "lodash/fp/omit";

import {
  apiUrls,
  buildUrlWithParams,
  UrlParams,
  CreatePlayerDto,
  isError,
  OrganizationId,
  AssignTeamDto,
  Player,
  PlayerQueryDto,
  TeamId,
  PopulatedPlayer,
  ChangeStatusDto,
  PlayerId,
  GetPlayerDto,
  ApplyToOrganizationDto,
  Assets,
  TeamEvent,
} from "@mio/helpers";

import { applyGenericErrorHandling } from "./genericErrorHandling";

export const makeCreateApplicant = (axiosClient: AxiosInstance) => (payload: CreatePlayerDto) => {
  return axiosClient
    .post(apiUrls.createApplicant, payload)
    .then(() => undefined)
    .catch(applyGenericErrorHandling);
};

export type MutateImageParams = {
  playerId: PlayerId;
  imageId: Assets.Image.ImageId;
};

export const makeGetPlayerDocumentImages =
  (axiosClient: AxiosInstance) =>
  (playerId: PlayerId): Promise<Assets.Image.ImageAsset[]> =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.player_documents.document_images, {
          [UrlParams.PlayerId]: playerId,
        }),
      )
      .then((response) => {
        const parsedImages = Assets.Image.Entity.toEntities(response.data);

        if (isError(parsedImages)) {
          parsedImages.addContext({
            service: makeGetPlayerDocumentImages.name,
            method: axiosClient.get.name,
            operation: Assets.Image.Entity.toEntities.name,
            params: { playerId },
          });
          throw parsedImages;
        }

        return parsedImages;
      });

export const makeSubmitDocuments = (axiosClient: AxiosInstance) => (playerId: PlayerId) =>
  axiosClient
    .patch(buildUrlWithParams(apiUrls.player_documents.submit, { [UrlParams.PlayerId]: playerId }))
    .then((response) => {
      const parsed = Player.toEntity(response.data);

      if (isError(parsed)) {
        parsed.addContext({
          service: makeSubmitDocuments.name,
          method: axiosClient.patch.name,
          operation: Player.toEntity.name,
          params: { playerId },
        });
        throw parsed;
      }
      return parsed;
    });

export const makeSetPlayerPhoto = (axiosClient: AxiosInstance) => (payload: MutateImageParams) =>
  axiosClient
    .patch(
      buildUrlWithParams(apiUrls.player_documents.photo, {
        [UrlParams.PlayerId]: payload.playerId,
        [UrlParams.PlayerImageId]: payload.imageId,
      }),
    )
    .then((response) => {
      const parsed = Player.toEntity(response.data);

      if (isError(parsed)) {
        parsed.addContext({
          service: makeSetPlayerPhoto.name,
          method: axiosClient.patch.name,
          operation: Player.toEntity.name,
          params: { playerId: payload.playerId, imageId: payload.imageId },
        });
        throw parsed;
      }
      return parsed;
    })
    .catch(applyGenericErrorHandling);

export const makeRemovePlayerPhoto = (axiosClient: AxiosInstance) => (payload: MutateImageParams) =>
  axiosClient
    .delete(
      buildUrlWithParams(apiUrls.player_documents.photo, {
        [UrlParams.PlayerId]: payload.playerId,
        [UrlParams.PlayerImageId]: payload.imageId,
      }),
    )
    .then((response) => {
      const parsed = Player.toEntity(response.data);

      if (isError(parsed)) {
        parsed.addContext({
          service: makeSetPlayerPhoto.name,
          method: axiosClient.patch.name,
          operation: Player.toEntity.name,
          params: { playerId: payload.playerId, imageId: payload.imageId },
        });
        throw parsed;
      }
      return parsed;
    })
    .catch(applyGenericErrorHandling);

export const makeUploadPlayerDocument =
  (axiosClient: AxiosInstance) => (payload: MutateImageParams) =>
    axiosClient
      .patch(
        buildUrlWithParams(apiUrls.player_documents.document_image, {
          [UrlParams.PlayerId]: payload.playerId,
          [UrlParams.PlayerImageId]: payload.imageId,
        }),
      )
      .then((response) => {
        const parsed = Player.toEntity(response.data);

        if (isError(parsed)) {
          parsed.addContext({
            service: makeSetPlayerPhoto.name,
            method: axiosClient.patch.name,
            operation: Player.toEntity.name,
            params: { playerId: payload.playerId, imageId: payload.imageId },
          });
          throw parsed;
        }
        return parsed;
      })
      .catch(applyGenericErrorHandling);

export const makeRemovePlayerDocument =
  (axiosClient: AxiosInstance) => (payload: MutateImageParams) =>
    axiosClient
      .delete(
        buildUrlWithParams(apiUrls.player_documents.document_image, {
          [UrlParams.PlayerId]: payload.playerId,
          [UrlParams.PlayerImageId]: payload.imageId,
        }),
      )
      .then((response) => {
        const parsed = Player.toEntity(response.data);

        if (isError(parsed)) {
          parsed.addContext({
            service: makeSetPlayerPhoto.name,
            method: axiosClient.patch.name,
            operation: Player.toEntity.name,
            params: { playerId: payload.playerId, imageId: payload.imageId },
          });
          throw parsed;
        }
        return parsed;
      })
      .catch(applyGenericErrorHandling);

export const makeUpdatePlayerProfile = (axiosClient: AxiosInstance) => (payload: Player) => {
  return axiosClient
    .patch(buildUrlWithParams(apiUrls.player, { [UrlParams.PlayerId]: payload.id }), payload)
    .then((response) => {
      const parsed = Player.toEntity(response.data);

      if (isError(parsed)) {
        throw parsed;
      }
      return parsed;
    })
    .catch(applyGenericErrorHandling);
};

export const makeAssignTeam =
  (axiosClient: AxiosInstance) =>
  async (payload: AssignTeamDto): Promise<PopulatedPlayer> => {
    return axiosClient
      .post(
        buildUrlWithParams(apiUrls.assignNewTeam, {
          [UrlParams.OrganizationId]: payload.organizationId,
        }),
        payload,
      )
      .then((result) => {
        const parsed = Player.toPopulatedEntity(result.data);

        if (isError(parsed)) {
          throw parsed;
        }

        return parsed;
      })
      .catch(applyGenericErrorHandling);
  };

export const makeChangeStatus =
  (axiosClient: AxiosInstance) =>
  async (payload: ChangeStatusDto): Promise<PopulatedPlayer> => {
    return axiosClient
      .post(
        buildUrlWithParams(apiUrls.changeTeamStatus, {
          [UrlParams.OrganizationId]: payload.organizationId,
        }),
        payload,
      )
      .then((result) => {
        const parsed = Player.toPopulatedEntity(result.data);

        if (isError(parsed)) {
          throw parsed;
        }

        return parsed;
      })
      .catch(applyGenericErrorHandling);
  };

export type RemovePlayerFromTeamPayload = {
  teamId: TeamId;
  playerId: PlayerId;
  organizationId: OrganizationId;
};

export const makeRemovePlayerFromTeam =
  (axiosClient: AxiosInstance) => async (payload: RemovePlayerFromTeamPayload) => {
    return axiosClient
      .delete(
        buildUrlWithParams(apiUrls.removeFromTeam, {
          [UrlParams.OrganizationId]: payload.organizationId,
          [UrlParams.TeamId]: payload.teamId,
          [UrlParams.PlayerId]: payload.playerId,
        }),
      )
      .then((response) => {
        const parsed = Player.toPopulatedEntity(response.data);

        if (isError(parsed)) {
          throw parsed;
        }

        return parsed;
      })
      .catch(applyGenericErrorHandling);
  };

export type RemovePlayerFromOrganizationPayload = {
  playerId: PlayerId;
  organizationId: OrganizationId;
};

export const makeRemovePlayerFromOrganization =
  (axiosClient: AxiosInstance) => async (payload: RemovePlayerFromOrganizationPayload) => {
    return axiosClient
      .delete(
        buildUrlWithParams(apiUrls.removeFromOrganization, {
          [UrlParams.OrganizationId]: payload.organizationId,
          [UrlParams.PlayerId]: payload.playerId,
        }),
      )
      .then((response) => {
        const parsed = Player.toPlayerId(response.data);

        if (isError(parsed)) {
          throw parsed;
        }

        return parsed;
      })
      .catch(applyGenericErrorHandling);
  };

export type QueryPlayersParams = {
  organizationId: OrganizationId;
  query: PlayerQueryDto;
};

export const makeQueryPlayers = (axiosClient: AxiosInstance) => (dto: QueryPlayersParams) =>
  axiosClient
    .get(
      buildUrlWithParams(apiUrls.searchOrganizationPlayers, {
        [UrlParams.OrganizationId]: dto.organizationId,
      }),
      { params: omit("orgId", dto.query), timeout: 10000 },
    )
    .then((res) => {
      const result = Player.toPaginatedPopulatedPlayers(res.data);

      if (isError(result)) {
        throw result;
      }

      return result;
    })
    .catch(applyGenericErrorHandling);

export const makeGetPlayerEventsUnansweredCount =
  (axiosClient: AxiosInstance) =>
  ({ playerId, query }: { playerId: PlayerId; query: TeamEvent.QueryDto }) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.playerEventsUnansweredCount, {
          [UrlParams.PlayerId]: playerId,
        }),
        { params: query },
      )
      .then((response) => TeamEvent.Entity.toUnansweredEventsCount(response.data))
      .catch(applyGenericErrorHandling);

export type GetTeamPlayerParams = {
  organizationId: OrganizationId;
  teamId: TeamId;
  query: PlayerQueryDto;
};

export const makeGetTeamPlayers = (axiosClient: AxiosInstance) => (dto: GetTeamPlayerParams) =>
  axiosClient
    .get(
      buildUrlWithParams(apiUrls.getTeamPlayers, {
        [UrlParams.OrganizationId]: dto.organizationId,
        [UrlParams.TeamId]: dto.teamId,
      }),
      { params: dto.query },
    )
    .then((res) => {
      const result = Player.toManyPopulatedEntities(res.data);

      if (isError(result)) {
        throw result;
      }

      return result;
    })
    .catch(applyGenericErrorHandling);

export const makeFindPlayer =
  (axiosClient: AxiosInstance) => (organizationId: OrganizationId, payload: GetPlayerDto) =>
    axiosClient
      .post(
        buildUrlWithParams(apiUrls.findPlayer, {
          [UrlParams.OrganizationId]: organizationId,
        }),
        payload,
      )
      .then((res) => !!res.data)
      .catch(applyGenericErrorHandling);

export type FindOrganizationPlayerDto = {
  playerId: PlayerId;
  organizationId: OrganizationId;
};

export const makeFindOrganizationPlayer =
  (axiosClient: AxiosInstance) => (payload: FindOrganizationPlayerDto) =>
    axiosClient
      .get(
        buildUrlWithParams(apiUrls.isPlayerInOrganization, {
          [UrlParams.OrganizationId]: payload.organizationId,
          [UrlParams.PlayerId]: payload.playerId,
        }),
      )
      .then((res) => !!res.data)
      .catch(applyGenericErrorHandling);

export const makeApplyToOrganization =
  (axiosClient: AxiosInstance) => (payload: ApplyToOrganizationDto) => {
    return axiosClient
      .post(
        buildUrlWithParams(apiUrls.applyToOrganization, {
          [UrlParams.OrganizationId]: payload.organizationId,
          [UrlParams.PlayerId]: payload.playerId,
        }),
      )
      .then(() => undefined)
      .catch(applyGenericErrorHandling);
  };

export const makeGetCurrentPlayers = (axiosClient: AxiosInstance) => () =>
  axiosClient
    .get(apiUrls.currentPlayers)
    .then((res) => {
      const result = Player.toManyEntities(res.data);

      if (isError(result)) {
        result.addContext({
          service: makeGetCurrentPlayers.name,
          method: axiosClient.get.name,
          operation: Player.toManyEntities.name,
          input: res.data,
        });

        throw result;
      }

      return result;
    })
    .catch(applyGenericErrorHandling);
