import { useLocation, useSearchParams } from "react-router-dom";

import { useLogoutContext } from "../../state/auth.shared";

export const useUrlSearchParamValues = () => {
  const [searchParams] = useSearchParams();

  const params = {} as Record<string, string>;

  searchParams.forEach((value, key) => {
    params[key] = value;
  });

  return params;
};

export const useUrlSearchParams = () => {
  const [searchParams] = useSearchParams();

  const params: string[] = [];

  searchParams.forEach((value, key) => {
    params.push(`${key}=${value}`);
  });

  return params.join("&");
};

export const useCreateReturnUrl = (baseUrl: string) => {
  const location = useLocation();
  const { didLogout } = useLogoutContext();

  if (didLogout && !location.search) {
    return baseUrl;
  }

  if (location.pathname === "/" && !location.search) {
    return baseUrl;
  }

  const returnUrl = encodeURIComponent(location.pathname + location.search);

  return baseUrl + `?${RETURN_URL}=${returnUrl}`;
};

export const useReturnUrl = () => {
  const [searchParams] = useSearchParams();

  const raw = searchParams.get(RETURN_URL);
  if (!raw) return "";

  try {
    return decodeURIComponent(raw);
  } catch {
    // Fallback to raw if malformed encoding
    return raw;
  }
};

const RETURN_URL = "returnUrl";
