import { FC, PropsWithChildren, ReactElement } from "react";
import { ZodObject, ZodRawShape } from "zod";

import { GenericRecord } from "@mio/helpers";

type Props = PropsWithChildren & {
  parser: ZodObject<ZodRawShape>;
  env: GenericRecord | NodeJS.ProcessEnv;
};

export const EnvConfigGuard: FC<Props> = (props) => {
  try {
    props.parser.parse(props.env);

    return props.children as ReactElement;
  } catch {
    throw new Error("Missing env variables.");
  }
};
