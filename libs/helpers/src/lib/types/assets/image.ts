import { z } from "zod";
import { truncate } from "lodash/fp";

import {
  CustomDate,
  CustomFile,
  CustomNumber,
  CustomUrl,
  StringOfLength,
  Tagged,
  UUID,
  withVersioning,
} from "../shared";
import { zodToDomain } from "../../misc";
import { Ownership } from "./ownership";

export type ImageId = Tagged<"ImageId", UUID>;

export const fileTypes = ["image/png", "image/jpeg"] as const;

/* up to 0.5 MB */
export const size = { max_bytes: 500000 } as const;

export const fileField = "file";

const fileParser = CustomFile.parser<typeof fileTypes, typeof size>(size, fileTypes);
export type ParsedFile = z.infer<typeof fileParser>;

const descriptionParser = StringOfLength.parser(0, 255, "image_description");

/* used only on the FE when uploading a file */
const clientCreateDto = z.object({
  file: fileParser,
  description: descriptionParser.optional(),
});
export type ClientCreateDto = z.infer<typeof clientCreateDto>;

/*
On the BE the "file" field will be plucked away by the file interceptor,
leaving only the optional description there 
*/
const createDto = z.object({
  description: descriptionParser.optional(),
});

export type CreateDto = z.infer<typeof createDto>;

export type UpdateDto = z.infer<typeof createDto>;

const parser = z
  .object({
    id: UUID.parser<ImageId>(),

    type: z.union([z.literal(fileTypes[0]), z.literal(fileTypes[1])]),
    name: StringOfLength.parser(1, 255, "image_name"),
    description: descriptionParser.optional(),
    url: CustomUrl.parser,
    size: CustomNumber.integerInRangeParser(0, size.max_bytes, "image_size"),
    ownership: z.union([Ownership.playerOwner, Ownership.coachOwner, Ownership.organizationOwner]),
  })
  .merge(withVersioning.pick({ createdAt: true }));

export type ImageAsset = z.infer<typeof parser>;

const mapExtension = (type: ImageAsset["type"]) => (type === fileTypes[0] ? "png" : "jpeg");

const generateName = (fileType: ParsedFile["type"]): ImageAsset["name"] =>
  `image_${UUID.generate()}.${mapExtension(fileType)}` as ImageAsset["name"];

export const Entity = {
  create(
    dto: CreateDto,
    file: ParsedFile,
    url: CustomUrl.Type,
    name: ImageAsset["name"],
    ownership: ImageAsset["ownership"],
    id = UUID.generate<ImageId>(),
    createdAt = CustomDate.now(),
  ): ImageAsset {
    return {
      id,
      size: file.size as ImageAsset["size"],
      type: file.type,
      name,
      description:
        dto.description || (truncate({ length: 50 }, file.name) as ImageAsset["description"]),
      url,
      ownership,
      createdAt,
    };
  },

  generateName,

  update(dto: UpdateDto, image: ImageAsset): ImageAsset {
    return {
      ...image,
      description: dto.description,
    };
  },

  toFile: (source: unknown) => zodToDomain(fileParser, source),
  parser: parser,

  /* used only on the front end when uploading a file */
  toClientCreateDto: (source: unknown) => zodToDomain(clientCreateDto, source),

  createDto: createDto,
  toCreateDto: (source: unknown) => zodToDomain(createDto, source),

  toEntity: (source: unknown) => zodToDomain(parser, source),
  toEntities: (source: unknown) => zodToDomain(parser.array(), source),

  toImageId: (source: unknown) => zodToDomain(UUID.parser<ImageId>(), source),
};
