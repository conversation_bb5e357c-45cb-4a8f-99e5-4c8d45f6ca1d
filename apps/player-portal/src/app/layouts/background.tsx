import { FC, ReactNode } from "react";

import { Box, useScreenSize, Stack } from "@mio/ui";

import boysImage from "../assets/boys.png";

type Props = {
  children: ReactNode;
  imageUrl?: string;
};

export const BackgroundLayout: FC<Props> = ({ children, imageUrl = boysImage }) => {
  const { smallScreen } = useScreenSize();

  if (smallScreen) {
    return (
      <Stack height="100%">
        <Box
          component="img"
          alt="Illustration of boys trying to reach a football ball stuck on a tree."
          src={imageUrl}
          sx={{
            maxWidth: "100%",
            maxHeight: "250px",
            objectFit: "cover",
          }}
        />
        <Box p={3}>{children}</Box>
      </Stack>
    );
  }

  return (
    <Stack
      sx={{
        height: "100%",
        flexDirection: "row",
        flexWrap: "wrap",
      }}
    >
      <Box
        sx={{
          flexGrow: 1,
          flexShrink: 1,
          maxWidth: "50%",
          overflow: "hidden",
        }}
      >
        <Box
          component="img"
          alt="Illustration of boys trying to reach a football ball stuck on a tree."
          src={imageUrl}
          sx={{
            maxHeight: "100vh",
            objectFit: "cover",
            objectPosition: "center",
          }}
        />
      </Box>

      <Stack
        alignItems="center"
        justifyContent="center"
        direction="row"
        flexGrow={1}
        flexShrink={1}
        minWidth="50%"
      >
        {children}
      </Stack>
    </Stack>
  );
};
