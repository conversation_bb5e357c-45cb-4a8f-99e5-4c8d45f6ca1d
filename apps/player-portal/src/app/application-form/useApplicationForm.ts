import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { stripHtml } from "string-strip-html";
import { useTranslation } from "react-i18next";

import {
  CustomDate,
  DateErrors,
  EmailErrors,
  Player,
  PlayingExperience,
  PublicOrganization,
} from "@mio/helpers";
import { ErrorFormatter, useFormResolver } from "@mio/ui";

import { DataShape } from "./types";

const createErrorFormatter = (t: (key: string) => string) =>
  ErrorFormatter.extendFormatter((message, fieldName) => {
    if (message === DateErrors.TooBig && fieldName === "dob") {
      return t("validation.dob.too-big");
    }

    if (message === DateErrors.TooSmall && fieldName === "dob") {
      return t("validation.dob.too-small");
    }

    if (fieldName === "email" && message === EmailErrors.SameGuardianPlayerEmail) {
      return t("validation.email.same-guardian-player-email");
    }

    return "";
  });

/* encapsulates form logic */
export const useApplicationForm = (organization: PublicOrganization) => {
  const { t } = useTranslation();
  const errorFormatter = createErrorFormatter(t);
  const formResolver = useFormResolver<DataShape>(Player.parseCreateDto, errorFormatter);

  const formMethods = useForm<DataShape>({
    defaultValues: {
      playingExperience: PlayingExperience.None,
      organizationId: organization.id,
    },
    resolver: formResolver,
    mode: "all",
  });

  const hasSubmitted = formMethods.formState.submitCount > 0;

  const { unregister } = formMethods;

  const data = formMethods.watch();
  const dob = formMethods.watch("dob");
  const guardianDob = formMethods.watch("guardian.dob");
  const playingExperience = formMethods.watch("playingExperience");
  const errors = formMethods.formState.errors;

  const age = CustomDate.getYearsAgo(dob);
  const requiresAGuardian = age > 0 && dob && Player.requiresAGuardian(dob);
  const requiresAnEmail = age > 0 && dob && Player.requiresAnEmail(dob);
  const acceptingApplications = !!organization.applications?.open;
  const acceptedTerms = !!formMethods.watch("acceptedTerms");

  /* omit html so we don't treat empty tags as real terms */
  const hasTerms = stripHtml(organization.privacyPolicy || "").result;
  const terms = organization.privacyPolicy || "";

  /* deals with toggable fields */
  useEffect(() => {
    if (!requiresAGuardian) {
      unregister("guardian");
    }
  }, [requiresAGuardian, unregister]);

  /* deals with toggable fields */
  useEffect(() => {
    if (!requiresAnEmail) {
      unregister("email");
    }
  }, [requiresAnEmail, unregister]);

  return {
    formMethods,
    guardianDob,
    data,
    playingExperience,
    acceptingApplications,
    dob,
    requiresAnEmail,
    requiresAGuardian,
    acceptedTerms,
    terms,
    hasTerms,
    age,
    hasSubmitted,
    errors: hasSubmitted ? errors : {},
  };
};
