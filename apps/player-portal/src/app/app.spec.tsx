import { render } from "@testing-library/react";
import { MemoryRouter } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { vi } from "vitest";

import { AppRouting } from "./routes";
import {
  SD<PERSON><PERSON><PERSON>ider,
  SDK,
  EnvConfig<PERSON><PERSON>,
  TokenContextProvider,
  configParser,
  StateProvider,
  ThemeProvider,
  createCustomTheme,
  DateProvider,
} from "@mio/ui";
import { environment } from "../environments/environment";

// testing for translation keys is a more stable practice
vi.mock("react-i18next", async () => {
  const actual = await vi.importActual("react-i18next");
  return {
    ...actual,
    useTranslation: () => ({
      t: (key: string) => key,
      i18n: {
        changeLanguage: () => new Promise(() => {}),
        language: "en",
      },
    }),
  };
});

describe("App", () => {
  // describe("when logged in", () => {
  //   const sdk = {
  //     player: {
  //       getCurrentUser: async () => ({
  //         id: UUID.generate(),
  //         type: "Player",
  //         authentication: {
  //           type: "OneTimePassword",
  //           email: "<EMAIL>",
  //           lastLogin: new Date(),
  //         },
  //       }),
  //       getCurrentPlayers: async () => [
  //         {
  //           id: UUID.generate(),
  //           firstName: "John",
  //           lastName: "Doe",
  //           dob: new Date(),
  //           gender: { name: "M" },
  //           playingExperience: "Club",
  //           acceptedTerms: true,
  //         },
  //       ],
  //     },
  //     payments: {
  //       getPlayerPayments: async () => [],
  //       createCheckoutSession: async () => ({}),
  //     },
  //   } as unknown as SDK;

  //   it("should render the dashboard", async () => {
  //     const queryClient = new QueryClient();
  //     const theme = createCustomTheme();

  //     const screen = render(
  //       <MemoryRouter initialEntries={["/players/1/dashboard"]}>
  //         <EnvConfigGuard env={environment} parser={configParser}>
  //           <TokenContextProvider>
  //             <StateProvider>
  //               <SDKProvider sdk={Promise.resolve(sdk)}>
  //                 <ThemeProvider theme={theme}>
  //                   <DateProvider>
  //                     <QueryClientProvider client={queryClient}>
  //                       <AppRouting />
  //                     </QueryClientProvider>
  //                   </DateProvider>
  //                 </ThemeProvider>
  //               </SDKProvider>
  //             </StateProvider>
  //           </TokenContextProvider>
  //         </EnvConfigGuard>
  //       </MemoryRouter>,
  //     );

  //     expect(await screen.findByText("Payments")).toBeTruthy();
  //   });
  // });

  describe("when not logged in", () => {
    const loggedOutSdk = {
      player: {
        getCurrentUser: async () => {
          throw new Error("Unauthorized");
        },
        getCurrentPlayers: async () => {
          throw new Error("Unauthorized");
        },
      },
      payments: {
        getPlayerPayments: async () => [],
        createCheckoutSession: async () => ({}),
      },
    } as unknown as SDK;

    it("should render the register (code sending step) page", async () => {
      const queryClient = new QueryClient();
      const theme = createCustomTheme();

      const screen = render(
        <MemoryRouter initialEntries={["/players/1/dashboard"]}>
          <EnvConfigGuard env={environment} parser={configParser}>
            <TokenContextProvider>
              <StateProvider>
                <SDKProvider sdk={Promise.resolve(loggedOutSdk)}>
                  <ThemeProvider theme={theme}>
                    <DateProvider>
                      <QueryClientProvider client={queryClient}>
                        <AppRouting />
                      </QueryClientProvider>
                    </DateProvider>
                  </ThemeProvider>
                </SDKProvider>
              </StateProvider>
            </TokenContextProvider>
          </EnvConfigGuard>
        </MemoryRouter>,
      );

      await screen.findByText("auth.register-page.title");
      await screen.findByLabelText("auth.register.email.label");
      await screen.findByRole("button", { name: "auth.register.submit" });
      await screen.findByRole("link", { name: "auth.register-page.enter-code-manually" });
    });
  });
});
