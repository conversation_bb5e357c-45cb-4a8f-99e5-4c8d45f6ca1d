import { FC } from "react";
import { useTranslation } from "react-i18next";

import {
  playersState,
  ImageUpload,
  LocalLoader,
  LocalError,
  UploadedImage,
  Stack,
  Card,
  FolderSharedIcon,
  Typography,
} from "@mio/ui";
import { Assets } from "@mio/helpers";

export const PlayerPassportUpload: FC = () => {
  const uploadPassportImage = playersState.useAddImageDocument();
  const player = playersState.useActivePlayer();

  const handleUpload = (file: Assets.Image.ParsedFile) => {
    uploadPassportImage.mutate({ playerId: player.id, dto: { file } });
  };

  if (uploadPassportImage.isLoading) {
    return <LocalLoader />;
  }

  return (
    <ImageUpload
      isLoading={uploadPassportImage.isLoading}
      onUpload={handleUpload}
      error={uploadPassportImage.error ?? undefined}
      success={uploadPassportImage.isSuccess}
    />
  );
};

export const UploadedPassportImages = () => {
  const { t } = useTranslation();
  const documentImagesQuery = playersState.usePlayerDocumentImages();

  if (documentImagesQuery.isLoading) {
    return <LocalLoader />;
  }

  if (documentImagesQuery.isError) {
    return <LocalError />;
  }

  return (
    <>
      {documentImagesQuery.data?.map((image) => (
        <Card
          key={image.id}
          sx={{
            p: 2,
            display: "flex",
            flexDirection: "column",
            gap: 3,
            maxWidth: { xs: "100%", md: "250px" },
          }}
        >
          <Stack direction="row" gap={2} alignItems="center">
            <FolderSharedIcon fontSize="large" />
            <Typography variant="h6" component="h2">
              {t("documents.passport.title")}
            </Typography>
          </Stack>

          <UploadedPlayerPassportPhoto key={image.id} image={image} />
        </Card>
      ))}
    </>
  );
};

type Props = {
  image: Assets.Image.ImageAsset;
};

const UploadedPlayerPassportPhoto: FC<Props> = ({ image }) => {
  const { t } = useTranslation();
  const player = playersState.useActivePlayer();
  const removePhoto = playersState.useRemoveImageDocument();

  const handleDelete = () => {
    removePhoto.mutate({ playerId: player.id, imageId: image.id });
  };

  return (
    <UploadedImage
      image={image}
      altText={t("documents.passport.title")}
      onRemove={handleDelete}
      isRemoving={removePhoto.isLoading}
      canRemove={player.documents?.submitted !== true}
      apiError={removePhoto.error ?? undefined}
      imageAction={
        <a href={image.url} target="_blank" rel="noreferrer">
          {t("documents.passport.view-image")}
        </a>
      }
    />
  );
};
