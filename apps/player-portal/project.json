{"name": "player-portal", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/player-portal/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/vite:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "dist/apps/player-portal", "configFile": "apps/player-portal/vite.config.ts"}, "configurations": {"development": {"mode": "development"}, "production": {"mode": "production", "fileReplacements": [{"replace": "apps/player-portal/src/environments/environment.ts", "with": "apps/player-portal/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nx/vite:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "player-portal:build"}, "configurations": {"development": {"buildTarget": "player-portal:build:development", "hmr": true}, "production": {"buildTarget": "player-portal:build:production", "hmr": false}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "type-check": {"executor": "nx:run-commands", "options": {"command": "npx tsc -b ./apps/player-portal"}}, "test": {"executor": "@nx/vite:test", "outputs": ["{options.reportsDirectory}"], "options": {"reportsDirectory": "../../coverage/apps/player-portal", "configFile": "apps/player-portal/vitest.config.ts"}}, "test:watch": {"executor": "@nx/vite:test", "outputs": ["{options.reportsDirectory}"], "options": {"reportsDirectory": "../../coverage/apps/player-portal", "configFile": "apps/player-portal/vitest.config.ts", "watch": true}}}}