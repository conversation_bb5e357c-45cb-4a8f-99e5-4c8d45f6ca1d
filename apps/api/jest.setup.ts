import td from "testdouble";
import * as Sentry from "@sentry/node";

process.env["DB_HOST"] = "mongodb://localhost:27017/coach-testing";
process.env["JWT_SECRET"] = "top secret";
process.env["API_KEY"] = "top secret as well";
process.env["SENTRY_KEY"] = "";
process.env["RESEND_API_KEY"] = "";
process.env["CLIENT_URL"] = "http://localhost:4001";
process.env["PLAYER_PORTAL_URL"] = "http://localhost:4001";
process.env["COACH_PORTAL_URL"] = "http://localhost:4002";
process.env["ENCRYPTION_KEY"] = "some key";
process.env["AZURE_BLOB_STORAGE_CONNECTION_STRING"] = "some key";
process.env["DISABLE_EMAILS"] = "true";
process.env["EMAIL_LOGO_URL"] = "https://ibb.co/rKsGVxFt";

td.replace(Sentry, "init");
