import { <PERSON>du<PERSON> } from "@nestjs/common";

import { TeamEventModule } from "../team-events/instances/team-event.module";
import { EmailModule } from "../email/email.module";
import { PlayerModule } from "../player/player.module";

import { RemindersService } from "./reminders.service";

@Module({
  imports: [TeamEventModule, EmailModule, PlayerModule],
  providers: [RemindersService],
})
export class RemindersModule {}
