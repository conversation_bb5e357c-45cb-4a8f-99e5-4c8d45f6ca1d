import { Injectable, Logger } from "@nestjs/common";
import { Cron, CronExpression } from "@nestjs/schedule";
import {
  TeamEvent,
  isError,
  CustomDate,
  Email,
  PlayerId,
  UUID,
  isNonEmptyArray,
} from "@mio/helpers";

import { TeamEventRepository } from "../team-events/instances/team-event.repository";
import { EmailService } from "../email/email.service";
import { PlayerRepository } from "../player/player.repository";

export const HANDLE_REMINDERS_CRON = "handle_reminders";

@Injectable()
export class RemindersService {
  private readonly logger = new Logger(RemindersService.name);

  constructor(
    private teamEventRepository: TeamEventRepository,
    private emailService: EmailService,
    private playerRepository: PlayerRepository,
  ) {}

  @Cron(CronExpression.EVERY_HOUR, { name: HANDLE_REMINDERS_CRON })
  async handleReminders() {
    this.logger.log("Running hourly event reminder check...");

    const events = await this.teamEventRepository.findEventsForReminder();

    if (isError(events)) {
      this.logger.error("Error fetching events for reminder", events);

      return;
    }

    if (events.length === 0) {
      this.logger.log("No events found requiring reminders.");
      return;
    }

    for (const event of events) {
      const invitedPlayerIds =
        event.type === TeamEvent.TeamEventType.Single
          ? event.invitations
          : event.recurringEvent.invitations;

      const respondentPlayerIds = (event.respondents || []).map((r) => r.player);

      const nonRespondingPlayerIds = invitedPlayerIds.filter(
        (id) => !respondentPlayerIds.includes(id),
      );

      if (nonRespondingPlayerIds.length === 0) {
        continue;
      }

      const playersToRemind: PlayerId[] = [];

      for (const playerId of nonRespondingPlayerIds) {
        const hasBeenNotified = TeamEvent.Entity.hasPlayerBeenNotified(
          event,
          playerId,
          TeamEvent.NotificationChannel.Email,
        );

        // for now we're only sending 1 reminder email per event per player
        if (!hasBeenNotified) {
          playersToRemind.push(playerId);
        }
      }

      if (playersToRemind.length === 0) {
        continue;
      }

      // todo: repo method which takes subscribe preferences into account
      const players = await this.playerRepository.getByIds(playersToRemind);

      if (isError(players)) {
        this.logger.error("Error fetching players for reminder", players);
        continue;
      }

      const playerEmails = players.flatMap((p) => {
        const emails = [];
        if (p.email) emails.push(p.email);
        if (p.guardian?.email) emails.push(p.guardian.email);
        return emails;
      });

      if (!isNonEmptyArray(playerEmails)) {
        continue;
      }

      await this.emailService.sendEventReminder(playerEmails, event);

      const now = CustomDate.now();

      // Create notification records for each player
      // We need to map players to their emails since playerEmails contains all emails (player + guardian)
      const notifications: TeamEvent.Notification[] = players.flatMap((player) => {
        const emailsForPlayer: Email[] = [];
        if (player.email) {
          emailsForPlayer.push(player.email);
        }
        if (player.guardian?.email) {
          emailsForPlayer.push(player.guardian.email);
        }

        return emailsForPlayer.map((email) => ({
          id: UUID.generate(),
          playerId: player.id,
          channel: TeamEvent.NotificationChannel.Email,
          recipient: email,
          status: TeamEvent.NotificationStatus.Sent,
          sentAt: now,
        }));
      });

      const updatedEvent = TeamEvent.Entity.addNotifications(event, notifications);

      const updateResult = await this.teamEventRepository.updateEvent(updatedEvent);

      if (isError(updateResult)) {
        this.logger.error(
          `Failed to update event ${event.id} with reminder timestamps`,
          updateResult,
        );
      } else {
        this.logger.log(
          `Sent reminders for event ${event.id} to ${playersToRemind.length} players.`,
        );
      }
    }
  }
}
