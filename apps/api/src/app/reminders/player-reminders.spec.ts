import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import { SchedulerRegistry } from "@nestjs/schedule";

import { AppModule } from "../app.module";
import { RemindersService } from "./reminders.service";
import { AppTestService } from "../test/test.service";
import { DatabaseModule } from "../database/database.module";
import { createInMemoryMongoClient } from "../../test/memory-mongo";
import { DATABASE_CLIENT, DATABASE_CONNECTION, DBClient } from "../database";
import { EmailService } from "../email/email.service";
import { clearAllMongoCollections } from "../../test";
import { playerFactory } from "../../test/factories/player";
import { HANDLE_REMINDERS_CRON } from "./reminders.service";

describe("RemindersService (e2e)", () => {
  let app: INestApplication;
  let testService: AppTestService;
  let mongoClient: DBClient;
  let scheduler: SchedulerRegistry;
  let remindersService: RemindersService;
  let moduleFixture: TestingModule;
  let emailMock: Partial<EmailService>;

  const triggerReminderCron = async () => {
    const job = scheduler.getCronJob(HANDLE_REMINDERS_CRON);
    job.fireOnTick();
    // Allow the async cron handler to run
    await new Promise((resolve) => setTimeout(resolve, 20));
  };

  beforeAll(async () => {
    const fakeMongo = await createInMemoryMongoClient();
    mongoClient = fakeMongo.db;

    emailMock = {
      sendEventReminder: jest.fn().mockResolvedValue(undefined),
    };

    moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
      providers: [],
    })
      .overrideModule(DatabaseModule)
      .useModule({
        module: class TestDatabaseModule {},
        imports: [],
        providers: [
          {
            provide: DATABASE_CONNECTION,
            useFactory: async () => fakeMongo.db,
          },
          {
            provide: DATABASE_CLIENT,
            useFactory: async () => fakeMongo.mongoClient,
          },
        ],
        exports: [DATABASE_CONNECTION, DATABASE_CLIENT],
      })
      .overrideProvider(EmailService)
      .useValue(emailMock)
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  beforeEach(async () => {
    testService = new AppTestService(mongoClient);
    scheduler = moduleFixture.get<SchedulerRegistry>(SchedulerRegistry);
    remindersService = moduleFixture.get<RemindersService>(RemindersService);
  });

  afterEach(async () => {
    // Clear all collections between test cases
    await clearAllMongoCollections(mongoClient);
    jest.resetAllMocks();
  });

  afterAll(async () => {
    await app.close();
  });

  describe("handleReminders()", () => {
    it("should not send reminders when there are no events", async () => {
      await triggerReminderCron();

      expect(emailMock.sendEventReminder).toHaveBeenCalledTimes(0);
    });

    it("should not send reminders when all players have responded", async () => {
      // Create a team event with a player who has already responded
      const { organization, profile: coachProfile } = await testService.getOrgWithCoach();
      const { team, applicants } = await testService.createTeamWithPlayers({
        organizationId: organization.id,
        applicantsCount: 1,
        type: "adult",
      });

      const player = applicants[0];

      if (!player.email) {
        throw new Error("Player email is undefined");
      }

      const teamEvent = await testService.createTeamEvent({
        teamId: team.id,
        organizationId: team.organizationId,
        invitations: [player.id],
        coachId: coachProfile.id,
      });

      // Player responds to the event
      await testService.upsertTeamEventResponse({
        teamEventId: teamEvent.id,
        playerId: player.id,
        status: "positive",
      });

      await triggerReminderCron();

      expect(emailMock.sendEventReminder).toHaveBeenCalledTimes(0);
    });

    it("should send reminders to players who haven't responded", async () => {
      // Create a team event with a player who hasn't responded
      const { organization, profile: coachProfile } = await testService.getOrgWithCoach();
      const { team, applicants } = await testService.createTeamWithPlayers({
        organizationId: organization.id,
        applicantsCount: 1,
        type: "adult",
      });

      const player = applicants[0];

      if (!player.email) {
        throw new Error("Player email is undefined");
      }

      const teamEvent = await testService.createTeamEvent({
        teamId: team.id,
        organizationId: team.organizationId,
        invitations: [player.id],
        coachId: coachProfile.id,
      });

      // Trigger the cron job
      await triggerReminderCron();

      // Verify that the event was updated with reminder information
      const updatedEvent = await testService.getTeamEventById(teamEvent.id);

      if (!updatedEvent) {
        throw new Error("Event not found in the database");
      }

      expect(emailMock.sendEventReminder).toHaveBeenCalledTimes(1);
      expect(updatedEvent.notifications?.length).toBe(1);
      expect(updatedEvent.notifications?.[0].playerId).toEqual(player.id);
      expect(updatedEvent.notifications?.[0].channel).toEqual("email");
      expect(updatedEvent.notifications?.[0].recipient).toEqual(player.email);
    });

    it("should not send duplicate reminders when no players remain to remind", async () => {
      const { organization, profile: coachProfile } = await testService.getOrgWithCoach();
      const { team, applicants } = await testService.createTeamWithPlayers({
        organizationId: organization.id,
        applicantsCount: 1,
        type: "adult",
      });

      const player = applicants[0];

      if (!player.email) {
        throw new Error("Player email is undefined");
      }

      const teamEvent = await testService.createTeamEvent({
        teamId: team.id,
        organizationId: team.organizationId,
        invitations: [player.id],
        coachId: coachProfile.id,
      });

      await triggerReminderCron();

      let updatedEvent = await testService.getTeamEventById(teamEvent.id);

      if (!updatedEvent) {
        throw new Error("Event not found in the database");
      }

      expect(emailMock.sendEventReminder).toHaveBeenCalledTimes(1);

      expect(updatedEvent.notifications?.length).toBe(1);

      await triggerReminderCron();

      updatedEvent = await testService.getTeamEventById(teamEvent.id);

      expect(emailMock.sendEventReminder).toHaveBeenCalledTimes(1);
      expect(updatedEvent?.notifications?.length).toBe(1);
    });

    it("should send reminders to guardian email when player has no email (age < 13)", async () => {
      const { organization, profile: coachProfile } = await testService.getOrgWithCoach();
      const team = await testService.createTeam({
        organizationId: organization.id,
        type: "youth",
      });

      const guardianEmail = "<EMAIL>";
      const player = playerFactory.createWithGuardianEmailOnly(organization.id, guardianEmail);

      await testService.savePlayer(player);
      await testService.joinOrganization({
        playerId: player.id,
        organizationId: organization.id,
      });

      const teamEvent = await testService.createTeamEvent({
        teamId: team.id,
        organizationId: team.organizationId,
        invitations: [player.id],
        coachId: coachProfile.id,
      });

      await triggerReminderCron();

      // Verify that reminder was sent to guardian email
      expect(emailMock.sendEventReminder).toHaveBeenCalledTimes(1);
      expect(emailMock.sendEventReminder).toHaveBeenCalledWith(
        [guardianEmail],
        expect.objectContaining({ id: teamEvent.id }),
      );

      // Verify that the event was updated with reminder information
      const updatedEvent = await testService.getTeamEventById(teamEvent.id);
      expect(updatedEvent?.notifications?.length).toBe(1);
      expect(updatedEvent?.notifications?.[0].playerId).toEqual(player.id);
      expect(updatedEvent?.notifications?.[0].channel).toEqual("email");
      expect(updatedEvent?.notifications?.[0].recipient).toEqual(guardianEmail);
    });

    it("should send reminders to both player and guardian emails when player has both (age 13-17)", async () => {
      const { organization, profile: coachProfile } = await testService.getOrgWithCoach();
      const team = await testService.createTeam({
        organizationId: organization.id,
        type: "default",
      });

      const playerEmail = "<EMAIL>";
      const guardianEmail = "<EMAIL>";
      const player = playerFactory.createWithBothEmails(
        organization.id,
        playerEmail,
        guardianEmail,
      );

      await testService.savePlayer(player);
      await testService.joinOrganization({
        playerId: player.id,
        organizationId: organization.id,
      });

      const teamEvent = await testService.createTeamEvent({
        teamId: team.id,
        organizationId: team.organizationId,
        invitations: [player.id],
        coachId: coachProfile.id,
      });

      await triggerReminderCron();

      // Verify that reminder was sent to both emails
      expect(emailMock.sendEventReminder).toHaveBeenCalledTimes(1);
      expect(emailMock.sendEventReminder).toHaveBeenCalledWith(
        expect.arrayContaining([playerEmail, guardianEmail]), // Should send to both emails
        expect.objectContaining({ id: teamEvent.id }),
      );

      const updatedEvent = await testService.getTeamEventById(teamEvent.id);
      expect(updatedEvent?.notifications?.length).toBe(2); // Both player and guardian emails
      expect(updatedEvent?.notifications?.every((n) => n.playerId === player.id)).toBe(true);
      expect(updatedEvent?.notifications?.some((n) => n.recipient === playerEmail)).toBe(true);
      expect(updatedEvent?.notifications?.some((n) => n.recipient === guardianEmail)).toBe(true);
    });

    it("should send reminders to mixed email types in same event", async () => {
      const { organization, profile: coachProfile } = await testService.getOrgWithCoach();
      const team = await testService.createTeam({
        organizationId: organization.id,
        type: "default",
      });

      // Create different types of players
      const guardianEmail1 = "<EMAIL>";
      const youngPlayer = playerFactory.createWithGuardianEmailOnly(
        organization.id,
        guardianEmail1,
      );

      const playerEmail = "<EMAIL>";
      const guardianEmail2 = "<EMAIL>";
      const teenPlayer = playerFactory.createWithBothEmails(
        organization.id,
        playerEmail,
        guardianEmail2,
      );

      const adultPlayer = playerFactory.createAdult(organization.id);

      // Save all players
      await testService.savePlayer(youngPlayer);
      await testService.savePlayer(teenPlayer);
      await testService.savePlayer(adultPlayer);

      // Join organization
      await testService.joinOrganization({
        playerId: youngPlayer.id,
        organizationId: organization.id,
      });
      await testService.joinOrganization({
        playerId: teenPlayer.id,
        organizationId: organization.id,
      });
      await testService.joinOrganization({
        playerId: adultPlayer.id,
        organizationId: organization.id,
      });

      const teamEvent = await testService.createTeamEvent({
        teamId: team.id,
        organizationId: team.organizationId,
        invitations: [youngPlayer.id, teenPlayer.id, adultPlayer.id],
        coachId: coachProfile.id,
      });

      await triggerReminderCron();

      // Verify that reminders were sent to correct emails
      expect(emailMock.sendEventReminder).toHaveBeenCalledTimes(1);
      expect(emailMock.sendEventReminder).toHaveBeenCalledWith(
        expect.arrayContaining([
          guardianEmail1, // Young player's guardian email
          playerEmail, // Teen player's own email
          guardianEmail2, // Teen player's guardian email (both should be sent)
          adultPlayer.email, // Adult player's own email
        ]),
        expect.objectContaining({ id: teamEvent.id }),
      );

      const updatedEvent = await testService.getTeamEventById(teamEvent.id);
      // 4 total notifications: 1 for youngPlayer, 2 for teenPlayer (player+guardian), 1 for adultPlayer
      expect(updatedEvent?.notifications?.length).toBe(4);
    });
  });
});
