import { z } from "zod";

import { CustomNumber } from "@mio/helpers";

export const AppConfig = z.object({
  DB_HOST: z.string(),
  JWT_SECRET: z.string(),
  SENTRY_KEY: z.string(),
  RESEND_API_KEY: z.string(),
  DISABLE_EMAILS: z.string().optional(), // "true" / "false"
  DEFAULT_EMAIL: z.string().optional(),
  /* TODO: change to ADMIN_API_KEY */
  API_KEY: z.string(),
  CRYPTO_SALT_ROUNDS: z.number().optional(),
  JWT_EXPIRATION: z.string().optional(),
  INVITE_DURATION_DAYS: CustomNumber.parsePositiveInteger.optional(),
  EMAIL_LOGO_URL: z.string().url(),

  COACH_PORTAL_URL: z.string(),
  PLAYER_PORTAL_URL: z.string(),

  ENCRYPTION_KEY: z.string(),

  AZURE_BLOB_STORAGE_CONNECTION_STRING: z.string(),
});

export type AppConfig = z.infer<typeof AppConfig>;

export const validate = (config: Record<string, unknown>) => {
  return AppConfig.parse(config);
};
