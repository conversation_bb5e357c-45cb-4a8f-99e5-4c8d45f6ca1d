import { Middleware<PERSON>onsumer, <PERSON><PERSON><PERSON>, RequestMethod } from "@nestjs/common";
import * as Sentry from "@sentry/node";
import "@sentry/tracing";
import { RewriteFrames } from "@sentry/integrations";
import { EventEmitterModule } from "@nestjs/event-emitter";
import { ScheduleModule } from "@nestjs/schedule";

import { AuthModule } from "./auth/auth.module";
import { AppConfigModule } from "./config";
import { InvitesModule } from "./invites/invites.module";
import { OrganizationModule } from "./organization/organization.module";
import { PlayerModule } from "./player";
import { ProfileModule } from "./profile/profile.module";
import { TeamModule } from "./team/team.module";
import { CoachUsersModule } from "./coach-users/users.module";
import { HealthCheckModule } from "./health-check/health-check.module";
import { SentryModule } from "../sentry/sentry.module";
import { EmailModule } from "./email/email.module";
import { SeasonModule } from "./season/season.module";
import { FinancialIntegrationModule } from "./financial-integration/financial-integration.module";
import { EncryptionModule } from "./encryption/encryption.module";
import { GoCardlessModule } from "./go-cardless/go-cardless.module";
import { TeamEventModule } from "./team-events/instances/team-event.module";
import { PlayerUsersModule } from "./player-users/player-users.module";
import { RecurringTeamEventModule } from "./team-events/recurring/recurring-team-event.module";
import { EmailTemplatesModule } from "./email-templates/email-templates.module";
import { DemoModule } from "./demo/demo.module";
import { TrainingSessionModule } from "./training-sessions/training-session.module";
import { AssetsModule } from "./assets/assets.module";
import { StripeModule } from "./stripe/stripe.module";
import { FootballMatchPlanModule } from "./match-centre";
import { FootballMatchReviewModule } from "./match-centre/football-match-review/football-match-review-module";
import { FootballMatchPlayerReviewModule } from "./match-centre/football-match-player-review/football-match-player-review.module";
import { FootballMatchAttemptAgainstModule } from "./match-centre/football-match-attempt-against/football-match-attempt-against.module";
import { FootballMatchAttemptForModule } from "./match-centre/football-match-attempt-for/football-match-attempt-for.module";
import { FootballMatchStatsModule } from "./match-stats/football-match-stats.module";
import { PaymentRequestsModule } from "./payments/payment-requests.module";
import { RemindersModule } from "./reminders/reminders.module";
import { TranslationsService } from "./translations/translations.service";

const rootDir = __dirname || process.cwd();

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    ScheduleModule.forRoot(),
    AppConfigModule,
    AuthModule,
    InvitesModule,
    HealthCheckModule,
    OrganizationModule,
    ProfileModule,
    SentryModule.forRoot({
      dsn: process.env.SENTRY_KEY,
      tracesSampleRate: 0,
      environment: process.env.NODE_ENV,
      debug: true,
      normalizeDepth: 0, //Avoid normalizing contexts to achieve better explainability
      maxValueLength: 1024 * 10, //Increase maximum length of payload value to 10Kb
      integrations: [
        new RewriteFrames({
          root: rootDir,
        }),
      ],
    }),
    TeamModule,
    CoachUsersModule,
    PlayerModule,
    EmailModule,
    SeasonModule,
    RecurringTeamEventModule,
    TeamEventModule,
    FinancialIntegrationModule,
    EncryptionModule,
    GoCardlessModule,
    PlayerUsersModule,
    EmailTemplatesModule,
    DemoModule,
    TrainingSessionModule,
    FootballMatchPlanModule,
    FootballMatchReviewModule,
    FootballMatchPlayerReviewModule,
    FootballMatchAttemptAgainstModule,
    FootballMatchAttemptForModule,
    FootballMatchStatsModule,
    AssetsModule,
    StripeModule,
    PaymentRequestsModule,
    RemindersModule,
  ],
  controllers: [],
  providers: [TranslationsService],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer): void {
    consumer.apply(Sentry.Handlers.requestHandler()).forRoutes({
      path: "*",
      method: RequestMethod.ALL,
    });
  }
}
