import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import request from "supertest";
import { addDays } from "date-fns";

import { AppModule } from "../app.module";
import { AppTestService } from "../test/test.service";
import { DatabaseModule } from "../database/database.module";
import { createInMemoryMongoClient } from "../../test/memory-mongo";
import { DATABASE_CLIENT, DATABASE_CONNECTION, DBClient } from "../database";
import { apiUrls, buildUrlWithParams, ErrorMessages, TeamEvent } from "@mio/helpers";
import { authenticatePlayer } from "../../test/player-auth";
import { clearAllMongoCollections } from "../../test";

describe("TeamEventsController (e2e)", () => {
  let app: INestApplication;
  let testService: AppTestService;
  let mongoClient: DBClient;

  beforeAll(async () => {
    const fakeMongo = await createInMemoryMongoClient();
    mongoClient = fakeMongo.db;

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
      providers: [],
    })
      .overrideModule(DatabaseModule)
      .useModule({
        module: class TestDatabaseModule {},
        imports: [],
        providers: [
          {
            provide: DATABASE_CONNECTION,
            useFactory: async () => fakeMongo.db,
          },
          {
            provide: DATABASE_CLIENT,
            useFactory: async () => fakeMongo.mongoClient,
          },
        ],
        exports: [DATABASE_CONNECTION, DATABASE_CLIENT],
      })
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    testService = new AppTestService(mongoClient);
  });

  afterEach(async () => {
    // Clear all collections between test cases
    await clearAllMongoCollections(mongoClient);
  });

  afterAll(async () => {
    await app.close();
  });

  describe("getPlayerEvents()", () => {
    it("returns list of upcoming events where player is invited", async () => {
      const { organization, profile: coachProfile } = await testService.getOrgWithCoach();
      const { team, applicants } = await testService.createTeamWithPlayers({
        organizationId: organization.id,
        applicantsCount: 1,
        type: "adult",
      });

      const player = applicants[0];

      if (!player.email) {
        throw new Error("Player email is undefined");
      }

      const teamEvent = await testService.createTeamEvent({
        teamId: team.id,
        organizationId: team.organizationId,
        invitations: [player.id],
        coachId: coachProfile.id,
      });

      // Create another team and an unrelated event to ensure the player only sees invited events
      const otherTeamData = await testService.createTeamWithPlayers({
        organizationId: organization.id,
        applicantsCount: 1,
        type: "adult",
      });

      const unrelatedPlayer = otherTeamData.applicants[0];

      await testService.createTeamEvent({
        teamId: otherTeamData.team.id,
        organizationId: otherTeamData.team.organizationId,
        invitations: [unrelatedPlayer.id],
        coachId: coachProfile.id,
      });

      // Create another event for the SAME player but beyond the requested endDate range (more than a week ahead)
      await testService.createTeamEvent({
        teamId: team.id,
        organizationId: team.organizationId,
        invitations: [player.id],
        coachId: coachProfile.id,
        overrides: {
          startDateTime: addDays(new Date(), 10),
          endDateTime: addDays(new Date(), 10),
          name: "Out-of-range Event",
        },
      });

      const { token } = await authenticatePlayer(app, player, testService);

      const response = await request(app.getHttpServer())
        .get(
          buildUrlWithParams(
            apiUrls.playerEvents,
            { playerId: player.id },
            {
              leadSlash: true,
            },
          ),
        )
        .query({
          startDate: new Date(0).toISOString(),
          endDate: addDays(new Date(), 7),
        })
        .set("Authorization", `Bearer ${token}`)
        .expect(200);

      expect(response.body).toHaveLength(1);
      expect(response.body[0].id).toEqual(teamEvent.id);
    });
  });

  describe("updatePlayerAttendance()", () => {
    it("allows a player to respond to a team event and saves their response", async () => {
      const { organization, profile: coachProfile } = await testService.getOrgWithCoach();
      const { team, applicants } = await testService.createTeamWithPlayers({
        organizationId: organization.id,
        applicantsCount: 1,
        type: "adult",
      });

      const player = applicants[0];

      if (!player.email) {
        throw new Error("Player email is undefined");
      }

      const teamEvent = await testService.createTeamEvent({
        teamId: team.id,
        organizationId: team.organizationId,
        invitations: [player.id],
        coachId: coachProfile.id,
      });

      // Player logs in
      const { token } = await authenticatePlayer(app, player, testService);

      // Player responds to the event with a positive response
      const response = await request(app.getHttpServer())
        .patch(
          buildUrlWithParams(
            apiUrls.playerTeamEvent,
            { playerId: player.id, teamEventId: teamEvent.id },
            { leadSlash: true },
          ),
        )
        .send({
          status: "positive",
        })
        .set("Authorization", `Bearer ${token}`)
        .expect(200);

      expect(response.body.id).toEqual(teamEvent.id);
      expect(response.body.respondents).toHaveLength(1);
      expect(response.body.respondents[0].player).toEqual(player.id);
      expect(response.body.respondents[0].status).toEqual("positive");

      // Verify in the database
      const eventInDb = await testService.getTeamEventById(teamEvent.id);
      if (!eventInDb) {
        throw new Error("Event not found in the database");
      }
      expect(eventInDb.respondents).toHaveLength(1);
      expect(eventInDb.respondents?.[0].player).toEqual(player.id);
      expect(eventInDb.respondents?.[0].status).toEqual("positive");
    });

    it("prevents a player from responding to an event they are not invited to", async () => {
      // Setup organization with coach and two teams
      const { organization, profile: coachProfile } = await testService.getOrgWithCoach();

      // First team and player (will be invited to the event)
      const {
        team,
        applicants: [invitedPlayer],
      } = await testService.createTeamWithPlayers({
        organizationId: organization.id,
        applicantsCount: 1,
        type: "adult",
      });

      // Second team and player (will NOT be invited to the event)
      const {
        applicants: [uninvitedPlayer],
      } = await testService.createTeamWithPlayers({
        organizationId: organization.id,
        applicantsCount: 1,
        type: "adult",
      });

      if (!uninvitedPlayer.email) {
        throw new Error("Uninvited player email is undefined");
      }

      // Create an event where only the first player is invited
      const teamEvent = await testService.createTeamEvent({
        teamId: team.id,
        organizationId: team.organizationId,
        invitations: [invitedPlayer.id], // Only invitedPlayer is invited
        coachId: coachProfile.id,
      });

      // Uninvited player logs in
      const { token } = await authenticatePlayer(app, uninvitedPlayer, testService);

      // Attempt to respond to the event
      const response = await request(app.getHttpServer())
        .patch(
          buildUrlWithParams(
            apiUrls.playerTeamEvent,
            { playerId: uninvitedPlayer.id, teamEventId: teamEvent.id },
            { leadSlash: true },
          ),
        )
        .send({
          status: "positive",
        })
        .set("Authorization", `Bearer ${token}`);

      // Should receive a 403 Forbidden response
      expect(response.status).toBe(403);
      expect(response.body.message).toBe(ErrorMessages.PermissionDenied);

      // Verify no response was recorded in the database
      const eventInDb = await testService.getTeamEventById(teamEvent.id);

      if (!eventInDb) {
        throw new Error("Event not found in the database");
      }

      // Should have no responses or only responses from other players
      const playerResponse = eventInDb.respondents?.find((r) => r.player === uninvitedPlayer.id);
      expect(playerResponse).toBeUndefined();
    });
  });

  describe("getUnansweredEventsCount()", () => {
    it("returns count of unanswered events for a player within date range", async () => {
      const { organization, profile: coachProfile } = await testService.getOrgWithCoach();
      const { team, applicants } = await testService.createTeamWithPlayers({
        organizationId: organization.id,
        applicantsCount: 1,
        type: "adult",
      });

      const player = applicants[0];
      if (!player.email) throw new Error("Player email is undefined");

      // Create 2 events for the player
      await testService.createTeamEvent({
        teamId: team.id,
        organizationId: organization.id,
        invitations: [player.id],
        coachId: coachProfile.id,
        overrides: {
          startDateTime: addDays(new Date(), 1), // Future event
          endDateTime: addDays(new Date(), 1),
        },
      });

      await testService.createTeamEvent({
        teamId: team.id,
        organizationId: organization.id,
        invitations: [player.id],
        coachId: coachProfile.id,
        overrides: {
          startDateTime: addDays(new Date(), 2), // Future event
          endDateTime: addDays(new Date(), 2),
        },
      });

      // Create an event outside the date range
      await testService.createTeamEvent({
        teamId: team.id,
        organizationId: organization.id,
        invitations: [player.id],
        coachId: coachProfile.id,
        overrides: {
          startDateTime: addDays(new Date(), 10), // Outside range
          endDateTime: addDays(new Date(), 10),
        },
      });

      // Create an event for a different player
      const {
        applicants: [otherPlayer],
      } = await testService.createTeamWithPlayers({
        organizationId: organization.id,
        applicantsCount: 1,
        type: "adult",
      });

      await testService.createTeamEvent({
        teamId: team.id,
        organizationId: organization.id,
        invitations: [otherPlayer.id], // Different player
        coachId: coachProfile.id,
      });

      const { token } = await authenticatePlayer(app, player, testService);
      const response = await request(app.getHttpServer())
        .get(
          buildUrlWithParams(
            apiUrls.playerEventsUnansweredCount,
            { playerId: player.id },
            { leadSlash: true },
          ),
        )
        .query({
          startDate: new Date().toISOString(),
          endDate: addDays(new Date(), 7).toISOString(),
        })
        .set("Authorization", `Bearer ${token}`)
        .expect(200);

      expect(response.body).toEqual({ count: 2 }); // Should only count the 2 events in date range for this player
    });

    it("includes recurring instances invited via parent in unanswered count", async () => {
      const { organization, profile: coachProfile } = await testService.getOrgWithCoach();
      const { team, applicants } = await testService.createTeamWithPlayers({
        organizationId: organization.id,
        applicantsCount: 1,
        type: "adult",
      });

      const player = applicants[0];
      if (!player.email) throw new Error("Player email is undefined");

      // Create three instances: two within range and one out of range via helper
      const startIn1 = addDays(new Date(), 1);
      const startIn2 = addDays(new Date(), 2);
      const outOfRange = addDays(new Date(), 10);

      await testService.createRecurringEventWithInstances({
        teamId: team.id,
        organizationId: organization.id,
        invitations: [player.id],
        coachId: coachProfile.id,
        instanceDates: [startIn1, startIn2, outOfRange],
        agenda: TeamEvent.Agenda.TrainingSession,
      });

      // Also add an unrelated single event for another player to ensure it doesn't affect the count
      const {
        applicants: [otherPlayer],
      } = await testService.createTeamWithPlayers({
        organizationId: organization.id,
        applicantsCount: 1,
        type: "adult",
      });

      await testService.createTeamEvent({
        teamId: team.id,
        organizationId: organization.id,
        invitations: [otherPlayer.id],
        coachId: coachProfile.id,
        overrides: {
          startDateTime: addDays(new Date(), 2),
          endDateTime: addDays(new Date(), 2),
        },
      });

      const { token } = await authenticatePlayer(app, player, testService);
      const res = await request(app.getHttpServer())
        .get(
          buildUrlWithParams(
            apiUrls.playerEventsUnansweredCount,
            { playerId: player.id },
            { leadSlash: true },
          ),
        )
        .query({
          startDate: new Date().toISOString(),
          endDate: addDays(new Date(), 7).toISOString(),
        })
        .set("Authorization", `Bearer ${token}`)
        .expect(200);

      // Expect only the two in-range recurring instances to be counted
      expect(res.body).toEqual({ count: 2 });
    });
  });
});
