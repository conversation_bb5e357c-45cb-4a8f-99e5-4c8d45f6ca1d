import { Inject, Injectable, Logger } from "@nestjs/common";

import {
  Primitive,
  UnexpectedError,
  isError,
  TeamId,
  WithVersioning,
  TeamEvent,
  ParsingError,
  CustomDate,
  PlayerId,
  DomainError,
  ErrorMessages,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../../database";
import { recurringTeamEventsCollection } from "../recurring/recurring-team-event.repository";
import { teamDbCollection } from "../../team/team.repository";
import { trainingSessionReviewCollection } from "../../training-sessions/training-session-review/training-session-review.repository";

export type TeamEventDocument = Primitive<TeamEvent.TeamEvent> & WithVersioning;

export const teamEventsCollection = "team-events";

@Injectable()
export class TeamEventRepository {
  private collection: DBCollection<TeamEventDocument>;
  private readonly logger = new Logger(TeamEventRepository.name);

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(teamEventsCollection);
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      await this.collection.createIndex({ id: 1 }, { unique: true });
      await this.collection.createIndex({ teamId: 1, startDateTime: 1, endDateTime: 1 });
      await this.collection.createIndex({ startDateTime: 1, endDateTime: 1 });
      await this.collection.createIndex({ invitations: 1 });
      await this.collection.createIndex({ "recurringEvent.invitations": 1 });
    } catch (err) {
      this.logger.error(`Error creating indexes for ${this.constructor.name}`, err);
    }
  }

  async createInBulk(
    events: TeamEvent.RecurringTeamEvent[],
    now = CustomDate.now(),
  ): Promise<UnexpectedError | void> {
    return this.collection
      .insertMany(events.map((event) => ({ ...event, createdAt: now })))
      .then(() => undefined)
      .catch((err) => {
        return new UnexpectedError(err, {
          service: TeamEventRepository.name,
          method: this.createInBulk.name,
          operation: this.collection.insertMany.name,
          events,
          createdAt: now,
        });
      });
  }

  async findEventsForReminder(): Promise<
    UnexpectedError | ParsingError | TeamEvent.ExtendedTeamEvent[]
  > {
    const now = CustomDate.now();
    const sevenDaysFromNow = CustomDate.addDays(7, () => now);

    const stages = [
      {
        $match: {
          startDateTime: { $gte: now, $lte: sevenDaysFromNow },
          isCancelled: { $ne: true },
        },
      },
      {
        $lookup: {
          from: recurringTeamEventsCollection,
          localField: "recurringEventId",
          foreignField: "id",
          as: "recurringEvent",
        },
      },
      {
        $unwind: {
          path: "$recurringEvent",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          invitationsCount: {
            $size: {
              $ifNull: [{ $ifNull: ["$invitations", "$recurringEvent.invitations"] }, []],
            },
          },
          respondentsCount: { $size: { $ifNull: ["$respondents", []] } },
        },
      },
      {
        $match: {
          $expr: { $gt: ["$invitationsCount", "$respondentsCount"] },
        },
      },
    ];

    return this.collection
      .aggregate(stages)
      .toArray()
      .then((result) => {
        if (result) {
          const parsed = TeamEvent.Entity.toExtendedEntities(result);

          if (isError(parsed)) {
            parsed.addContext({
              service: TeamEventRepository.name,
              method: this.findEventsForReminder.name,
              operation: TeamEvent.Entity.toExtendedEntities.name,
              data: result,
            });
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: TeamEventRepository.name,
          method: this.findEventsForReminder.name,
        });
      });
  }

  async create(
    event: TeamEvent.SingleTeamEvent,
    now = CustomDate.now(),
  ): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({ ...event, createdAt: now })
      .then(() => undefined)
      .catch((err) => {
        return new UnexpectedError(err, {
          service: TeamEventRepository.name,
          method: this.create.name,
          operation: this.collection.insertOne.name,
          event,
          createdAt: now,
        });
      });
  }

  async getPlayerEvent(
    id: TeamEvent.EventId,
  ): Promise<UnexpectedError | ParsingError | TeamEvent.PlayerTeamEvent | null> {
    const stages = [
      {
        $match: { id },
      },
      {
        $lookup: {
          from: recurringTeamEventsCollection,
          localField: "recurringEventId",
          foreignField: "id",
          as: "recurringEvent",
        },
      },
      {
        $unwind: {
          path: "$recurringEvent",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: teamDbCollection,
          localField: "teamId",
          foreignField: "id",
          as: "team",
        },
      },
      { $unwind: "$team" },
    ];

    return this.collection
      .aggregate(stages)
      .toArray()
      .then((result) => {
        if (result?.length > 0) {
          const parsed = TeamEvent.Entity.toPlayerEventEntity(result[0]);

          if (isError(parsed)) {
            parsed.addContext({
              service: TeamEventRepository.name,
              method: this.getPlayerEvent.name,
              operation: TeamEvent.Entity.toPlayerEventEntity.name,
              data: result,
              id,
            });
          }

          return parsed;
        }

        return null;
      });
  }

  async getPlayerEvents(
    playerId: PlayerId,
    query: TeamEvent.QueryDto,
  ): Promise<UnexpectedError | ParsingError | TeamEvent.PlayerTeamEvent[]> {
    /*

    1. Filter all events within the time range
    2. Combine each event with its parent RecurringEvent
    3. Filter again - the player must be in event.invitations[] or 
       event.recurringEvent.invitations[]; agenda must match
    4. Add event.team to each element to help the FE do less requests

    */

    const matchStage = {
      startDateTime: { $gte: query.startDate },
      endDateTime: { $lte: query.endDate },
    };

    const stages = [
      {
        $match: matchStage,
      },
      {
        $lookup: {
          from: recurringTeamEventsCollection,
          localField: "recurringEventId",
          foreignField: "id",
          as: "recurringEvent",
        },
      },
      {
        $unwind: {
          path: "$recurringEvent",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          $or: [{ invitations: playerId }, { "recurringEvent.invitations": playerId }],
        },
      },
      {
        $match: {
          ...(query.agenda
            ? {
                $or: [{ agenda: query.agenda }, { "recurringEvent.agenda": query.agenda }],
              }
            : undefined),
        },
      },
      {
        $lookup: {
          from: teamDbCollection,
          localField: "teamId",
          foreignField: "id",
          as: "team",
        },
      },
      { $unwind: "$team" },
      {
        $sort: { startDateTime: 1 },
      },
    ];

    return this.collection
      .aggregate(stages)
      .toArray()
      .then((result) => {
        if (result) {
          const parsed = TeamEvent.Entity.toPlayerEventEntities(result);

          if (isError(parsed)) {
            parsed.addContext({
              service: TeamEventRepository.name,
              method: this.getPlayerEvents.name,
              operation: TeamEvent.Entity.toPlayerEventEntities.name,
              data: result,
              playerId,
              query,
            });
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: TeamEventRepository.name,
          method: this.getPlayerEvents.name,
          playerId,
          query,
        });
      });
  }

  async getTeamEvents(
    teamId: TeamId,
    query: TeamEvent.QueryDto,
  ): Promise<UnexpectedError | ParsingError | TeamEvent.ExtendedTeamEvent[]> {
    const matchStage = {
      teamId,
      ...(query.startDate ? { startDateTime: { $gte: query.startDate } } : undefined),
      ...(query.endDate ? { endDateTime: { $lte: query.endDate } } : undefined),
    };

    const stages = [
      {
        $match: matchStage,
      },
      {
        $lookup: {
          from: recurringTeamEventsCollection,
          localField: "recurringEventId",
          foreignField: "id",
          as: "recurringEvent",
        },
      },
      {
        $unwind: {
          path: "$recurringEvent",
          preserveNullAndEmptyArrays: true,
        },
      },
      { $match: { $or: [{ agenda: query.agenda }, { "recurringEvent.agenda": query.agenda }] } },
      {
        $lookup: {
          from: trainingSessionReviewCollection,
          localField: "id",
          foreignField: "teamEventId",
          as: "trainingSessionReview",
        },
      },
      {
        $unwind: {
          path: "$trainingSessionReview",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $sort: { createdAt: 1 },
      },
    ];

    return this.collection
      .aggregate(stages)
      .toArray()
      .then((result) => {
        if (result) {
          const parsed = TeamEvent.Entity.toExtendedEntities(result);

          if (isError(parsed)) {
            parsed.addContext({
              service: TeamEventRepository.name,
              method: this.getTeamEvents.name,
              operation: TeamEvent.Entity.toExtendedEntities.name,
              data: result,
              teamId,
              query,
            });
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: TeamEventRepository.name,
          method: this.getTeamEvents.name,
          teamId,
          query,
        });
      });
  }

  async getExtendedEvent(
    id: TeamEvent.EventId,
  ): Promise<UnexpectedError | ParsingError | DomainError | TeamEvent.ExtendedTeamEvent | null> {
    /*
    Get a TeamEvent by id and attach its' parent RecurringEvent to it if such exists
    */

    const stages = [
      {
        $match: { id },
      },
      {
        $lookup: {
          from: recurringTeamEventsCollection,
          localField: "recurringEventId",
          foreignField: "id",
          as: "recurringEvent",
        },
      },
      {
        $unwind: {
          path: "$recurringEvent",
          preserveNullAndEmptyArrays: true,
        },
      },
    ];

    return this.collection
      .aggregate(stages)
      .toArray()
      .then((result) => {
        if (result) {
          /* we expect only 1 TeamEvent therefore we have the checks below */

          if (result.length === 0) {
            return null;
          }

          if (result.length > 1) {
            return new DomainError(ErrorMessages.MultipleEntitiesFound, {
              service: TeamEventRepository.name,
              method: this.getExtendedEvent.name,
              eventId: id,
            });
          }

          const parsed = TeamEvent.Entity.toExtendedEntity(result[0]);

          if (isError(parsed)) {
            parsed.addContext({
              service: TeamEventRepository.name,
              method: this.getExtendedEvent.name,
              operation: TeamEvent.Entity.toExtendedEntity.name,
              data: result,
              eventId: id,
            });
          }

          return parsed;
        }

        return null;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: TeamEventRepository.name,
          method: this.getExtendedEvent.name,
          eventId: id,
        });
      });
  }

  async updateEvent(event: TeamEvent.TeamEvent): Promise<void | UnexpectedError | DomainError> {
    return this.collection
      .updateOne(
        { id: event.id },
        {
          $set: {
            ...event,
            updatedAt: CustomDate.now(),
          },
        },
      )
      .then((res) => {
        if (res.modifiedCount < 1) {
          return new DomainError(ErrorMessages.EntityNotFound, {
            service: TeamEventRepository.name,
            method: this.updateEvent.name,
            operation: this.collection.updateOne.name,
            event,
          });
        }

        if (res.modifiedCount > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound, {
            service: TeamEventRepository.name,
            method: this.updateEvent.name,
            operation: this.collection.updateOne.name,
            event,
          });
        }

        return undefined;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: TeamEventRepository.name,
          method: this.updateEvent.name,
          operation: this.collection.updateOne.name,
          event,
        });
      });
  }

  async getUnansweredEventsCount(
    playerId: PlayerId,
    query: TeamEvent.QueryDto,
  ): Promise<UnexpectedError | number> {
    const matchStage = {
      startDateTime: { $gte: query.startDate },
      endDateTime: { $lte: query.endDate },
    };

    const stages = [
      { $match: matchStage },
      {
        $lookup: {
          from: recurringTeamEventsCollection,
          localField: "recurringEventId",
          foreignField: "id",
          as: "recurringEvent",
        },
      },
      {
        $unwind: {
          path: "$recurringEvent",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          $or: [{ invitations: playerId }, { "recurringEvent.invitations": playerId }],
          "respondents.player": { $ne: playerId },
        },
      },
      {
        $count: "unansweredEvents",
      },
    ];

    return this.collection
      .aggregate(stages)
      .toArray()
      .then((result) => {
        if (result.length > 0) {
          return result[0].unansweredEvents;
        }
        return 0;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: TeamEventRepository.name,
          method: this.getUnansweredEventsCount.name,
          playerId,
          query,
        });
      });
  }

  async getEventById(
    id: TeamEvent.EventId,
  ): Promise<UnexpectedError | ParsingError | TeamEvent.TeamEvent | null> {
    return this.collection
      .findOne({
        id,
      })
      .then((response) => {
        if (response) {
          const parsed = TeamEvent.Entity.toEntity(response);

          if (isError(parsed)) {
            parsed.addContext({
              service: TeamEventRepository.name,
              method: this.getEventById.name,
              operation: TeamEvent.Entity.toEntity.name,
              id,
              payload: response,
            });
          }

          return parsed;
        }
        return null;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: TeamEventRepository.name,
          method: this.getEventById.name,
          operation: this.collection.findOne.name,
          id,
        });
      });
  }
}
