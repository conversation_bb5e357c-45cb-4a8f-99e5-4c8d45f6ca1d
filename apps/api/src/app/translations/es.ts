export const translations = {
  emails: {
    invite: {
      greeting: "<PERSON><PERSON>",
      body: "Has sido invitado a registrarte con {{organizationName}} en Team-Assist. Por favor sigue el enlace y completa tu registro.",
      thanks: "<PERSON><PERSON><PERSON>",
      signature: "Team-Assist",
      subject: "Has sido invitado a registrarte con {{organizationName}}",
    },
    newApplicant: {
      title: "¡Bienvenido a Team Assist!",
      body: 'Puedes terminar de crear tu perfil de jugador y usarlo en el futuro para mantenerte conectado con tus equipos confirmando tu correo electrónico <a target="_blank" href="{{url}}">aquí</a>. Solo haz clic en el enlace y sigue las instrucciones.',
      optOut: "Si deseas optar por no participar, no tienes que hacer nada.",
      subject: "Team Assist: Portal del Jugador",
    },
    playerLogin: {
      title: "Inicia sesión en tu cuenta del Portal del Jugador de Team Assist.",
      clickHereText: "Haz clic aquí para iniciar sesión",
      validityNote: "El enlace será válido durante 15 minutos.",
      codeInstructionsPrefix: "O ingresa el código manualmente:",
      codeInstructionsAt: "en",
      subject: "Team Assist: Portal del Jugador - enlace de inicio de sesión",
    },
    passwordReset: {
      title: "Recientemente has solicitado restablecer la contraseña de tu cuenta de Team Assist.",
      instructions:
        '<a target="_blank" href="{{url}}">Haz clic en este enlace para cambiar tu contraseña</a>',
      validityNote: "Será válido durante 15 minutos.",
      subject: "Team Assist - restablecer contraseña",
    },
    marketing: {
      title: "Has recibido un nuevo correo de {{fullName}}, su correo electrónico es: ✉️{{email}}",
      messageLabel: "Mensaje: {{message}}",
      subject: "[Lead del sitio web] : {{subject}}",
    },
    eventReminder: {
      title: "Recordatorio de evento",
      greeting: "Hola",
      body: "Este es un recordatorio amistoso de que tienes un próximo evento: {{eventName}} el {{eventDate}} a las {{eventTime}}. ¡Por favor, haznos saber si puedes asistir!",
      buttonText: "Ver evento y responder",
      fallbackText:
        "Si el botón de arriba no funciona, puedes ver el evento copiando y pegando el siguiente enlace en tu navegador:",
      subject: "Recordatorio de tu próximo evento: {{eventName}}",
    },
  },
} as const;
