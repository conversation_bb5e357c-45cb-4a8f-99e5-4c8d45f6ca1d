export const translations = {
  emails: {
    invite: {
      greeting: "Hi",
      body: "You have been invited to register with {{organizationName}} for Team-Assist. Please follow the link and complete your registration.",
      thanks: "Thank you",
      signature: "Team-Assist",
      subject: "You have been invited to register with {{organizationName}}",
    },
    newApplicant: {
      title: "Welcome to Team Assist!",
      body: 'You can finish creating your player profile and use it in future to stay connected to your teams by confirming your email <a target="_blank" href="{{url}}">here</a>. Just click the link and follow the instructions.',
      optOut: "If you wish to opt out, you don't have to do anything.",
      subject: "Team Assist: Player Portal",
    },
    playerLogin: {
      title: "Login to your Team Assist Player Portal account.",
      clickHereText: "Click here to login",
      validityNote: "The link will be valid for 15 minutes.",
      codeInstructionsPrefix: "Or enter the code manually:",
      codeInstructionsAt: "at",
      subject: "Team Assist: Player Portal - login link",
    },
    passwordReset: {
      title: "You've recently requested a password reset for your Team Assist account.",
      instructions: '<a target="_blank" href="{{url}}">Click this link to change your password</a>',
      validityNote: "It will be valid for 15 minutes.",
      subject: "Team Assist - reset password",
    },
    marketing: {
      title: "You've got a new mail from {{fullName}}, their email is: ✉️{{email}}",
      messageLabel: "Message: {{message}}",
      subject: "[Lead from website] : {{subject}}",
    },
    eventReminder: {
      title: "Event Reminder",
      greeting: "Hi there",
      body: "This is a friendly reminder that you have an upcoming event: {{eventName}} on {{eventDate}} at {{eventTime}}. Please let us know if you can make it!",
      buttonText: "View Event & Respond",
      fallbackText:
        "If the button above does not work, you can view the event by copying and pasting the following link in your browser:",
      subject: "Reminder for your upcoming event: {{eventName}}",
    },
  },
} as const;
