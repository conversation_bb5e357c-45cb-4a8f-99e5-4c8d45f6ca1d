import { Inject, Injectable } from "@nestjs/common";
import { match } from "ts-pattern";

import { organizationFactory } from "../../test/factories/organization";
import {
  Invite,
  must,
  Organization,
  Prettify,
  Primitive,
  Profile,
  PermissionsModule,
  Team,
  PlayerTeamProfile,
  Player,
  Team<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Coach<PERSON>ser,
  RecurringTeamEvent,
} from "@mio/helpers";
import { inviteFactory } from "../../test/factories/invite";
import { teamEventFactory } from "../../test/factories/team-event";
import { coachUserFactory } from "../../test/factories/coach-user";
import { profileFactory } from "../../test/factories/profile";
import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";
import { permissionFactory, roleFactory } from "../../test/factories/permissions";
import { type SimplifiedTeam, teamFactory } from "../../test/factories/team";
import { playerFactory } from "../../test/factories/player";
import { playerUserFactory } from "../../test/factories/player-user";
import { recurringTeamEventsCollection } from "../team-events/recurring/recurring-team-event.repository";

@Injectable()
export class AppTestService {
  private organizationCollection: DBCollection<Prettify<Primitive<Organization>>>;
  private invitesCollection: DBCollection<Prettify<Primitive<Invite>>>;
  private coachUsersCollection: DBCollection<Prettify<Primitive<CoachUser>>>;
  private profileCollection: DBCollection<Prettify<Primitive<Profile>>>;
  private rolesCollection: DBCollection<Prettify<Primitive<PermissionsModule.Role.Role>>>;
  private permissionsCollection: DBCollection<
    Prettify<Primitive<PermissionsModule.PermissionEntity.Permission>>
  >;
  private teamsCollection: DBCollection<Prettify<Primitive<Team>>>;
  private playerTeamProfileCollection: DBCollection<Prettify<Primitive<PlayerTeamProfile>>>;
  private playerCollection: DBCollection<Prettify<Primitive<Player>>>;
  private teamEventsCollection: DBCollection<Prettify<Primitive<TeamEvent.TeamEvent>>>;
  private playerUsersCollection: DBCollection<Prettify<Primitive<PlayerUser.PlayerUser>>>;
  private recurringTeamEventsCollection: DBCollection<
    Prettify<Primitive<RecurringTeamEvent.RecurringTeamEvent>>
  >;

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.organizationCollection = db.collection("organizations");
    this.invitesCollection = db.collection("invites");
    this.coachUsersCollection = db.collection("users");
    this.profileCollection = db.collection("profiles");
    this.rolesCollection = db.collection("roles");
    this.permissionsCollection = db.collection("permissions");
    this.teamsCollection = db.collection("teams");
    this.playerTeamProfileCollection = db.collection("player-team-profiles");
    this.playerCollection = db.collection("players");
    this.teamEventsCollection = db.collection("team-events");
    this.playerUsersCollection = db.collection("player-users");
    this.recurringTeamEventsCollection = db.collection(recurringTeamEventsCollection);
  }

  async saveCoachUser(user: CoachUser) {
    return await this.coachUsersCollection.insertOne(user);
  }

  async saveProfile(profile: Profile) {
    return await this.profileCollection.insertOne(profile);
  }

  async saveInvite(invite: Invite) {
    return await this.invitesCollection.insertOne(invite);
  }

  async getOrgWithCoach(props?: {
    firstName?: string;
    lastName?: string;
    coachEmail?: string;
    orgId?: Organization["id"];
  }) {
    const adminEmail = "<EMAIL>";

    const organization = await match(props?.orgId)
      .with(undefined, async () => {
        const draft = organizationFactory.create({
          name: "Test Organization",
          displayName: "Test Organization Display",
          contactEmail: adminEmail,
          senderEmail: adminEmail,
        });

        await this.organizationCollection.insertOne(draft);
        const reloadedOrg = await this.organizationCollection.findOne({ id: draft.id });

        return Organization.toEntityOrThrow(reloadedOrg);
      })
      .otherwise(async () =>
        Organization.toEntityOrThrow(
          await this.organizationCollection.findOne({ id: props?.orgId }),
        ),
      );

    const pendingInvite = inviteFactory.createPending(organization.id, {
      email: adminEmail,
      organization: organization.id,
    });

    await this.invitesCollection.insertOne(pendingInvite);

    const coachUser = coachUserFactory.create({
      authentication: {
        email: props?.coachEmail ?? adminEmail,
        password: "kochkata442",
        type: "credentials",
      },
    });

    await this.coachUsersCollection.insertOne(coachUser);

    const profile = profileFactory.create(coachUser.id, {
      firstName: props?.firstName ?? "John",
      lastName: props?.lastName ?? "Doe",
    });

    await this.profileCollection.insertOne(profile);

    const orgWithMember = Organization.addMember(organization, profile.id);

    await this.organizationCollection.updateOne({ id: organization.id }, { $set: orgWithMember });

    const redeemedInvite = Invite.redeem(pendingInvite);

    await this.invitesCollection.updateOne({ id: redeemedInvite.id }, { $set: redeemedInvite });

    return {
      organization: orgWithMember,
      pendingInvite,
      redeemedInvite,
      coachUser,
      profile,
    };
  }

  async joinOrganization(props: { playerId: Player["id"]; organizationId: Organization["id"] }) {
    const { playerId, organizationId } = props;

    const player = await this.playerCollection
      .findOne({ id: playerId })
      .then(Player.toEntityOrThrow);

    const applicant = PlayerTeamProfile.createApplicant({
      organizationId,
      playerId,
      firstName: player.firstName,
      lastName: player.lastName,
      email: player.email,
      phone: player.phone,
      dob: player.dob,
      gender: player.gender,
      playingExperience: player.playingExperience,
      playingExperienceDescription: player.playingExperienceDescription,
      medicalConditions: player.medicalConditions,
      address: player.address,
      acceptedTerms: player.acceptedTerms,
    });

    await this.playerTeamProfileCollection.insertOne(applicant);

    return { applicant, player };
  }

  async addOwnerPermission(props: {
    organizationId: Organization["id"];
    profileId: Profile["id"];
  }) {
    const { organizationId, profileId } = props;

    const permission = permissionFactory.createOwner(organizationId, profileId);

    await this.permissionsCollection.insertOne(permission);

    return { permission };
  }

  async addCustomPermission(props: {
    actions?: PermissionsModule.Action.Actions[];
    roleId?: PermissionsModule.Role.RoleId;
    organizationId: Organization["id"];
    profileId: Profile["id"];
  }) {
    const {
      actions = Object.values(PermissionsModule.Action.Actions),
      organizationId,
      profileId,
      roleId,
    } = props;

    const role = await match(roleId)
      .with(undefined, async () => {
        const role = roleFactory.create(organizationId, {
          name: "Custom Role",
          description: "Custom Role",
          actions,
        });

        await this.rolesCollection.insertOne(role);
        const reloadedRole = await this.rolesCollection.findOne({ id: role.id });

        return PermissionsModule.Role.Role.toEntityOrThrow(reloadedRole);
      })
      .otherwise(async (roleId) =>
        PermissionsModule.Role.Role.toEntityOrThrow(
          must(await this.rolesCollection.findOne({ id: roleId })),
        ),
      );

    const permission = permissionFactory.createCustom(organizationId, profileId, role.id);

    await this.permissionsCollection.insertOne(permission);

    return {
      role,
      permission,
    };
  }

  async createTeam(props: {
    organizationId: Organization["id"];
    type: "youth" | "adult" | "default";
    overrides?: Partial<SimplifiedTeam>;
  }): Promise<Team> {
    const team = match(props.type)
      .with("youth", () => teamFactory.createYouth(props.organizationId, props?.overrides))
      .with("adult", () => teamFactory.createAdult(props.organizationId, props?.overrides))
      .with("default", () => teamFactory.create(props.organizationId, props?.overrides))
      .exhaustive();

    await this.teamsCollection.insertOne(team);

    return team;
  }

  async createTeamWithPlayers(props: {
    organizationId: Organization["id"];
    applicantsCount: number;
    type: "youth" | "adult" | "default";
    overrides?: Partial<SimplifiedTeam>;
  }) {
    const team = await this.createTeam(props);
    const applicants = Array.from({ length: props.applicantsCount }).map(() =>
      playerFactory.create(team.organizationId, props.type),
    );
    const profiles = applicants.map((applicant) => {
      return PlayerTeamProfile.createApplicant({
        organizationId: team.organizationId,
        playerId: applicant.id,
        firstName: applicant.firstName,
        lastName: applicant.lastName,
        email: applicant.email,
        phone: applicant.phone,
        dob: applicant.dob,
        gender: applicant.gender,
        playingExperience: applicant.playingExperience,
        playingExperienceDescription: applicant.playingExperienceDescription,
        medicalConditions: applicant.medicalConditions,
        address: applicant.address,
        acceptedTerms: applicant.acceptedTerms,
      });
    });

    await this.playerCollection.insertMany(applicants);
    await this.playerTeamProfileCollection.insertMany(profiles);

    return { team, applicants, profiles };
  }

  async createTeamEvent(props: {
    teamId: Team["id"];
    organizationId: Organization["id"];
    invitations: Player["id"][];
    coachId: Profile["id"];
    overrides?: {
      startDateTime?: Date;
      endDateTime?: Date;
      name?: string;
      description?: string;
    };
  }) {
    const teamEvent = teamEventFactory.createSingle({
      teamId: props.teamId,
      organizationId: props.organizationId,
      invitations: props.invitations,
      hosts: [props.coachId],
      ...((props.overrides as Partial<TeamEvent.SingleTeamEvent>) ?? {}),
    });

    await this.teamEventsCollection.insertOne(teamEvent);

    return teamEvent;
  }

  async getTeamEventById(id: TeamEvent.TeamEvent["id"]) {
    return await this.teamEventsCollection.findOne({ id });
  }

  async createRecurringEventWithInstances(props: {
    teamId: Team["id"];
    organizationId: Organization["id"];
    invitations: Player["id"][];
    coachId: Profile["id"];
    instanceDates: Date[];
    agenda?: TeamEvent.Agenda;
  }) {
    const startFrom = new Date(Math.min(...props.instanceDates.map((d) => d.getTime())));
    const endDate = new Date(Math.max(...props.instanceDates.map((d) => d.getTime())));

    const parent = teamEventFactory.createRecurringParent({
      teamId: props.teamId,
      organizationId: props.organizationId,
      invitations: props.invitations,
      hosts: [props.coachId],
      agenda: props.agenda ?? TeamEvent.Agenda.TrainingSession,
      startFrom,
      endDate,
    });

    await this.recurringTeamEventsCollection.insertOne(parent);

    const instances = props.instanceDates.map((d) =>
      teamEventFactory.createRecurringInstance(parent, d),
    );

    await this.teamEventsCollection.insertMany(instances);

    return { parent, instances };
  }

  async createPlayerUser(email: string) {
    const playerUser = playerUserFactory.create({ email });
    await this.playerUsersCollection.insertOne(playerUser);
    return playerUser;
  }

  async savePlayer(player: Player) {
    return await this.playerCollection.insertOne(player);
  }

  async createOrgWithUserInvite(props: { inviteStatus: "pending" | "expired" | "redeemed" }) {
    const adminEmail = "<EMAIL>";

    const organization = organizationFactory.create({
      name: "Test Organization",
      displayName: "Test Organization Display",
      contactEmail: adminEmail,
      senderEmail: adminEmail,
    });

    await this.organizationCollection.insertOne(organization);

    const invite = match(props.inviteStatus)
      .with("pending", () =>
        inviteFactory.createPending(organization.id, {
          email: adminEmail,
          organization: organization.id,
        }),
      )
      .with("expired", () =>
        inviteFactory.createExpired(organization.id, {
          email: adminEmail,
          organization: organization.id,
        }),
      )
      .with("redeemed", () =>
        inviteFactory.createRedeemed(organization.id, {
          email: adminEmail,
          organization: organization.id,
        }),
      )
      .exhaustive();

    await this.invitesCollection.insertOne(invite);

    return {
      organization,
      invite,
    };
  }
  async upsertTeamEventResponse(props: {
    teamEventId: TeamEvent.TeamEvent["id"];
    playerId: Player["id"];
    status: "positive" | "negative" | "maybe";
  }) {
    const { teamEventId, playerId, status } = props;

    // Find the event
    const event = await this.teamEventsCollection.findOne({ id: teamEventId });

    if (!event) {
      throw new Error(`Event not found: ${teamEventId}`);
    }

    // Update or add the respondent
    const respondents = event.respondents || [];

    // Check if the player has already responded
    const existingResponseIndex = respondents.findIndex((r) => r.player === playerId);

    if (existingResponseIndex > -1) {
      // Update existing response
      respondents[existingResponseIndex] = {
        player: playerId,
        status,
        createdAt: new Date(),
      };
    } else {
      // Add new response
      respondents.push({
        player: playerId,
        status,
        createdAt: new Date(),
      });
    }

    // Update the event
    const updatedEvent = {
      ...event,
      respondents,
    };

    await this.teamEventsCollection.updateOne({ id: teamEventId }, { $set: updatedEvent });

    return updatedEvent;
  }
}
