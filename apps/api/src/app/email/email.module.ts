import { <PERSON><PERSON><PERSON> } from "@nestjs/common";

import { SentryModule } from "../../sentry/sentry.module";
import { AppConfigModule } from "../config";
import { OrganizationSharedModule } from "../organization/organization.shared.module";
import { EmailController } from "./email.controller";
import { EmailService } from "./email.service";
import { EmailListenerService } from "./email-listener.service";
import { TranslationsModule } from "../translations/translations.module";
import { DatabaseModule } from "../database";
import { ProfileModule } from "../profile/profile.module";
import { CoachUsersRepository } from "../coach-users/users-repository";

@Module({
  imports: [
    OrganizationSharedModule,
    AppConfigModule,
    SentryModule,
    TranslationsModule,
    DatabaseModule,
    ProfileModule,
  ],
  providers: [EmailService, EmailListenerService, CoachUsersRepository],
  controllers: [EmailController],
  exports: [EmailService],
})
export class EmailModule {}
