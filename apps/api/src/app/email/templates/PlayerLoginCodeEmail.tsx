import { Body, Container, Head, Heading, Html, Img, Link, Text } from "@react-email/components";
import React, { type CSSProperties } from "react";

import { Locale, TranslationsService } from "../../translations/translations.service";

interface PlayerLoginCodeEmailProps {
  translationsService: TranslationsService;
  locale: Locale;
  url: string;
  baseUrl: string;
  code: string;
  logoUrl: string;
}

export const PlayerLoginCodeEmail = ({
  translationsService,
  locale,
  url,
  baseUrl,
  code,
  logoUrl,
}: PlayerLoginCodeEmailProps) => (
  <Html>
    <Head />
    <Body style={bodyStyle}>
      <Container style={containerStyle}>
        <Img src={logoUrl} alt="Team Assist logo" style={logoStyle} />
        <Heading style={headingStyle}>
          {translationsService.translate("emails.playerLogin.title", locale)}
        </Heading>
        <Text>
          <Link href={url} target="_blank">
            {translationsService.translate("emails.playerLogin.clickHereText", locale)}
          </Link>
        </Text>
        <Text>{translationsService.translate("emails.playerLogin.validityNote", locale)}</Text>
        <Text>
          {translationsService.translate("emails.playerLogin.codeInstructionsPrefix", locale)}{" "}
          <Text
            style={{
              fontWeight: "bold",
              letterSpacing: 1.5,
              padding: 8,
              backgroundColor: "#f4f4f4",
              borderRadius: 4,
            }}
          >
            {code}
          </Text>{" "}
          {translationsService.translate("emails.playerLogin.codeInstructionsAt", locale)}{" "}
          <Link href={baseUrl} target="_blank">
            {baseUrl}
          </Link>
        </Text>
      </Container>
    </Body>
  </Html>
);

const bodyStyle: CSSProperties = {
  backgroundColor: "#f4f4f4",
  margin: 0,
};

const containerStyle: CSSProperties = {
  margin: "0 auto",
  padding: "20px",
  backgroundColor: "#ffffff",
  fontFamily: "Arial, sans-serif",
};

const logoStyle: CSSProperties = {
  margin: "0 auto 16px",
  display: "block",
  width: "120px",
};

const headingStyle: CSSProperties = {
  fontSize: "20px",
  lineHeight: "24px",
  marginBottom: "16px",
};
