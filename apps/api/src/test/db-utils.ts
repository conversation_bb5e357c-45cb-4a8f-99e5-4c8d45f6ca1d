import { DBClient } from "../app/database";

/**
 * Clear all documents from every collection in the provided Mongo DB client.
 * Keeps indexes intact and is safe to run between test cases.
 */
export const clearAllMongoCollections = async (db: DBClient): Promise<void> => {
  const collections = await db.collections();
  // Use sequential deletes to reduce the chance of race conditions across test files
  // when sharing the same in-memory Mongo instance.
  for (const collection of collections) {
    await collection.deleteMany({});
  }
};
