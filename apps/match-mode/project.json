{"name": "match-mode", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/vite:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "dist/apps"}, "configurations": {"development": {"mode": "development"}, "production": {"mode": "production", "fileReplacements": [{"replace": "apps/match-mode/src/environments/environment.ts", "with": "apps/match-mode/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nx/vite:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "match-mode:build"}, "configurations": {"development": {"buildTarget": "match-mode:build:development", "hmr": true}, "production": {"buildTarget": "match-mode:build:production", "hmr": false, "fileReplacements": [{"replace": "apps/match-mode/src/environments/environment.ts", "with": "apps/match-mode/src/environments/environment.prod.ts"}]}}}, "preview": {"dependsOn": ["build"], "executor": "@nx/vite:preview-server", "defaultConfiguration": "development", "options": {"buildTarget": "match-mode:build"}, "configurations": {"development": {"buildTarget": "match-mode:build:development"}, "production": {"buildTarget": "match-mode:build:production"}}}, "test": {"executor": "@nx/vite:test", "outputs": ["{options.reportsDirectory}"], "options": {"reportsDirectory": "../coverage/apps", "configFile": "apps/match-mode/vitest.config.ts"}}, "test:watch": {"executor": "@nx/vite:test", "options": {"watch": true}}, "lint": {"executor": "@nx/eslint:lint"}, "serve-static": {"executor": "@nx/web:file-server", "dependsOn": ["build"], "options": {"buildTarget": "match-mode:build", "spa": true}}, "type-check": {"executor": "nx:run-commands", "options": {"command": "npx tsc -b ./apps/match-mode"}}}}