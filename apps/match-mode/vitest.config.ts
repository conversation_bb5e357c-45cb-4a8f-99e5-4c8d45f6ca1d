import { mergeConfig } from "vite";
import { defineConfig } from "vitest/config";
import viteConfig from "./vite.config";

export default mergeConfig(
  viteConfig,
  defineConfig({
    test: {
      watch: false,
      globals: true,
      environment: "jsdom",
      silent: true,
      include: ["src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}"],
      reporters: ["default"],
      setupFiles: ["./src/test/setup.ts"],
      coverage: {
        reportsDirectory: "../../coverage/apps",
        provider: "v8",
      },
      alias: {
        // Redirects imports of 'crypto' to use Node's built-in crypto
        // This prevents <PERSON><PERSON><PERSON> from trying to resolve crypto as an npm package
        // and failing
        crypto: "node:crypto",
      },
    },
  }),
);
