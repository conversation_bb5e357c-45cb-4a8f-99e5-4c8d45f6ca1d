/// <reference types='vitest' />
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import { nxViteTsPaths } from "@nx/vite/plugins/nx-tsconfig-paths.plugin";
import { nxCopyAssetsPlugin } from "@nx/vite/plugins/nx-copy-assets.plugin";
import { VitePWA } from "vite-plugin-pwa";

export default defineConfig({
  root: __dirname,
  cacheDir: "../../node_modules/.vite/apps",
  server: {
    port: 4200,
    host: "localhost",
  },
  preview: {
    port: 4300,
    host: "localhost",
  },
  plugins: [
    react(),
    nxViteTsPaths(),
    nxCopyAssetsPlugin(["*.md"]),
    VitePWA({
      registerType: "autoUpdate",
      includeAssets: ["favicon.ico"],
      manifest: {
        name: "Match Mode",
        short_name: "Match Mode",
        description: "Match Mode Application",
        theme_color: "#ffffff",
        icons: [
          {
            src: "pwa-192x192.png",
            sizes: "192x192",
            type: "image/png",
          },
          {
            src: "pwa-512x512.png",
            sizes: "512x512",
            type: "image/png",
          },
          {
            src: "pwa-512x512.png",
            sizes: "512x512",
            type: "image/png",
            purpose: "any maskable",
          },
        ],
      },
      workbox: {
        // Increase the maximum file size to cache
        maximumFileSizeToCacheInBytes: 4 * 1024 * 1024, // 4MB
      },
    }),
  ],
  // Uncomment this if you are using workers.
  // worker: {
  //  plugins: [ nxViteTsPaths() ],
  // },
  build: {
    outDir: "../../dist/apps",
    emptyOutDir: true,
    reportCompressedSize: true,
    commonjsOptions: {
      transformMixedEsModules: true,
    },
    // Increase the chunk size warning limit
    chunkSizeWarningLimit: 1000, // 1000kb
    rollupOptions: {
      output: {
        // Implement manual chunks for better code splitting
        manualChunks: {
          react: ["react", "react-dom", "react-router-dom"],
          mui: [
            "@mui/material",
            "@mui/icons-material",
            "@mui/lab",
            "@mui/x-data-grid",
            "@mui/x-date-pickers",
          ],
          tanstack: ["@tanstack/react-query", "@tanstack/react-query-devtools"],
          dexie: ["dexie", "dexie-react-hooks"],
          vendors: ["date-fns", "lodash", "zod", "immer"],
        },
      },
    },
  },
});
