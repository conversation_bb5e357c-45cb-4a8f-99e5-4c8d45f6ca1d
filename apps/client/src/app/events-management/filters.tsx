import { FC } from "react";
import { useForm, Controller } from "react-hook-form";
import { omitBy, isObject } from "lodash/fp";
import { useTranslation } from "react-i18next";

import { Box, Stack, DatePicker, Button, SearchIcon, useFormResolver, FormControl } from "@mio/ui";
import { CustomDate, CustomNumber, isError, Primitive, TeamEvent } from "@mio/helpers";

type DataShape = Primitive<TeamEvent.QueryDto>;

type Props = {
  isLoading?: boolean;
  onSubmit: (dto: TeamEvent.QueryDto) => void;
};

export const TeamEventsFilters: FC<Props> = ({ onSubmit, isLoading }) => {
  const { t } = useTranslation();
  const initialDatePeriod = TeamEvent.Entity.constructFutureRange(
    CustomNumber.castToPositiveInteger(30),
  );

  const formResolver = useFormResolver<DataShape>(TeamEvent.Entity.toQueryDto);

  const { handleSubmit, watch, control } = useForm<DataShape>({
    defaultValues: {
      startDate: initialDatePeriod.startDate,
      endDate: initialDatePeriod.endDate,
    },
    mode: "onChange",
    resolver: formResolver,
  });

  const query = watch();

  const handleFormSubmit = (data: unknown) => {
    const withoutEmptyFields = omitBy((value) => value === "", isObject(data) ? data : {});

    const parsed = TeamEvent.Entity.toQueryDto(withoutEmptyFields);

    if (!isError(parsed)) {
      onSubmit({ ...parsed });
    }
  };

  return (
    <Box mt={1} component="form" onSubmit={handleSubmit(handleFormSubmit)}>
      <Stack direction="row" alignItems="center" flexWrap="wrap" gap={2}>
        <FormControl sx={{ width: { xs: 1, md: 1 / 5 } }}>
          <Controller
            name="startDate"
            control={control}
            render={({ field: { onChange, ...restField } }) => (
              <DatePicker
                label={t("events.filters.start-date")}
                format="dd/MM/y"
                openTo="year"
                onChange={(newValue) => {
                  onChange(CustomDate.validOrEmpty(newValue));
                }}
                {...restField}
              />
            )}
          />
        </FormControl>
        <FormControl sx={{ width: { xs: 1, md: 1 / 5 } }}>
          <Controller
            name="endDate"
            control={control}
            render={({ field: { onChange, ...restField } }) => (
              <DatePicker
                label={t("events.filters.end-date")}
                format="dd/MM/y"
                openTo="year"
                minDate={query.startDate}
                onChange={(newValue) => {
                  onChange(CustomDate.validOrEmpty(newValue));
                }}
                {...restField}
              />
            )}
          />
        </FormControl>
        <Stack justifyContent="center" alignItems="center" ml={3}>
          <Button
            type="submit"
            startIcon={<SearchIcon />}
            loading={isLoading}
            variant="contained"
            color="secondary"
            size="large"
          >
            {t("events.filters.show")}
          </Button>
        </Stack>
      </Stack>
    </Box>
  );
};
