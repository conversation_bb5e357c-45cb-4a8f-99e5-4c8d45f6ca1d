import { FC } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";

import {
  Stack,
  FormLabel,
  InputLabel,
  Select,
  MenuItem,
  DatePicker,
  Divider,
  FormControl,
  TimePicker,
  visuallyHidden,
  Typography,
} from "@mio/ui";
import {
  EventScheduling,
  CustomDate,
  PositiveInteger,
  Season,
  RecurringTeamEvent,
} from "@mio/helpers";

type Props = {
  currentSeason: Season | null;
};

const SchedulingForm: FC<Props> = ({ currentSeason }) => {
  const { t } = useTranslation();
  const { watch, errors } = useSchedulingForm();

  const type = watch("schedule.type") || "";
  const durationType = watch("schedule.duration.type");

  return (
    <Stack component="fieldset" gap={2} sx={{ borderStyle: "none" }}>
      <Divider>
        <FormLabel component="legend" sx={{ mb: 1 }}>
          {t("events.scheduling.title")}
        </FormLabel>
      </Divider>

      <FormControl>
        <Controller<RecurringTeamEvent.RecurringTeamEvent>
          name="schedule.startTime"
          shouldUnregister
          defaultValue={CustomDate.now()}
          render={({ field: { onChange, value, ...restField } }) => (
            <TimePicker
              label={t("events.scheduling.start-time")}
              onChange={(newValue) => {
                onChange(CustomDate.validOrEmptyTime(newValue));
              }}
              value={value as Date | null}
              slotProps={{
                textField: {
                  fullWidth: true,
                  helperText: errors.schedule?.startTime?.message || "",
                  error: !!errors.schedule?.startTime?.message,
                  "aria-invalid": !!errors.schedule?.startTime?.message,
                },
              }}
              {...restField}
            />
          )}
        />
      </FormControl>

      <FormControl>
        <Controller<RecurringTeamEvent.RecurringTeamEvent>
          name="schedule.endTime"
          shouldUnregister
          defaultValue={CustomDate.addMinutes(60 as PositiveInteger)}
          render={({ field: { onChange, value, ...restField } }) => (
            <TimePicker
              label={t("events.scheduling.end-time")}
              onChange={(newValue) => {
                onChange(CustomDate.validOrEmptyTime(newValue));
              }}
              value={value as Date | null}
              slotProps={{
                textField: {
                  fullWidth: true,
                  helperText: errors.schedule?.endTime?.message || "",
                  error: !!errors.schedule?.endTime?.message,
                  "aria-invalid": !!errors.schedule?.endTime?.message,
                },
              }}
              {...restField}
            />
          )}
        />
      </FormControl>

      <FormControl>
        <Controller<RecurringTeamEvent.RecurringTeamEvent>
          name="schedule.startFrom"
          shouldUnregister
          defaultValue={CustomDate.now()}
          render={({ field: { onChange, value, ...restField } }) => (
            <DatePicker
              label={t("events.scheduling.start-from")}
              format="dd/MM/y"
              onChange={(newValue) => {
                onChange(CustomDate.validOrEmpty(newValue));
              }}
              value={value as Date | null}
              {...restField}
            />
          )}
        />
      </FormControl>

      <FormControl>
        <InputLabel htmlFor="schedule.type">{t("events.scheduling.type")}</InputLabel>
        <Controller<RecurringTeamEvent.RecurringTeamEvent>
          name="schedule.type"
          defaultValue={EventScheduling.Type.Weekly}
          render={(params) => (
            <Select
              fullWidth
              inputProps={{ ...params.field }}
              label={t("events.scheduling.type")}
              id="schedule.type"
            >
              <MenuItem value={EventScheduling.Type.Weekly}>
                {t("events.scheduling.types.weekly")}
              </MenuItem>
              <MenuItem value={EventScheduling.Type.BiWeekly}>
                {t("events.scheduling.types.bi-weekly")}
              </MenuItem>
              <MenuItem value={EventScheduling.Type.Monthly}>
                {t("events.scheduling.types.monthly")}
              </MenuItem>
            </Select>
          )}
        />
      </FormControl>

      {[EventScheduling.Type.Weekly, EventScheduling.Type.BiWeekly].includes(type) && (
        <Controller<RecurringTeamEvent.RecurringTeamEvent>
          name="schedule.day"
          shouldUnregister
          defaultValue={EventScheduling.getToday()}
          render={(params) => (
            <FormControl>
              <InputLabel htmlFor="event-scheduling-day">Day of week</InputLabel>

              <Select
                fullWidth
                inputProps={{ ...params.field }}
                id="event-scheduling-day"
                label="Day of week"
              >
                {Object.entries(EventScheduling.DayOfWeek).map(([key, value]) => (
                  <MenuItem key={value} value={value}>
                    {key}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}
        />
      )}

      <Divider />

      <FormControl>
        <InputLabel htmlFor="schedule.duration.type">Ends</InputLabel>
        <Controller<RecurringTeamEvent.RecurringTeamEvent>
          name="schedule.duration.type"
          defaultValue={EventScheduling.EndCriteria.CustomDate}
          render={(params) => (
            <Select
              fullWidth
              required
              id="schedule.duration.type"
              label="Ends"
              inputProps={{ ...params.field }}
            >
              <MenuItem value={EventScheduling.EndCriteria.EndOfSeason} disabled={!currentSeason}>
                End Of Season{" "}
                {!!currentSeason && (
                  <Typography fontSize="small" display="inline" component="span" sx={{ ml: 1 }}>
                    ({CustomDate.toDisplayDate(currentSeason.endDate)})
                  </Typography>
                )}
              </MenuItem>
              <MenuItem value={EventScheduling.EndCriteria.CustomDate}>Custom date</MenuItem>
            </Select>
          )}
        />
      </FormControl>

      {durationType === EventScheduling.EndCriteria.EndOfSeason && currentSeason && (
        <FormControl sx={visuallyHidden}>
          <Controller<RecurringTeamEvent.RecurringTeamEvent>
            name="schedule.duration.seasonId"
            defaultValue={currentSeason.id}
            shouldUnregister
            render={() => <div />}
          />
        </FormControl>
      )}
      {durationType === EventScheduling.EndCriteria.CustomDate && (
        <FormControl>
          <Controller<RecurringTeamEvent.RecurringTeamEvent>
            name="schedule.duration.date"
            shouldUnregister
            defaultValue={CustomDate.addDays(60 as PositiveInteger)}
            render={({ field: { onChange, value, ...restField } }) => (
              <DatePicker
                label="End date"
                onChange={(newValue) => {
                  onChange(CustomDate.validOrEmpty(newValue));
                }}
                value={value as Date | null}
                format="dd/MM/y"
                {...restField}
              />
            )}
          />
        </FormControl>
      )}
    </Stack>
  );
};
const useSchedulingForm = () => {
  const { register, watch, formState } = useFormContext<RecurringTeamEvent.RecurringTeamEvent>();
  const { errors, submitCount } = formState;
  const hasSubmitted = submitCount > 0;
  return {
    register,
    watch,
    errors: hasSubmitted ? errors : {},
  };
};
export default SchedulingForm;
