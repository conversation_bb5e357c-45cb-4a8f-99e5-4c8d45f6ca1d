import { FC } from "react";
import { useTranslation } from "react-i18next";

import { TeamEvent, CustomDate } from "@mio/helpers";
import { Stack, Typography, Divider, Chip } from "@mio/ui";

type Props = {
  event: TeamEvent.ExtendedTeamEvent;
};

const eventTypeKeys = {
  [TeamEvent.TeamEventType.Single]: "events.type.single",
  [TeamEvent.TeamEventType.Recurring]: "events.type.recurring",
} as const;

const EventItem: FC<Props> = ({ event }) => {
  const { t } = useTranslation();

  return (
    <Stack gap={1} key={event.id}>
      <Stack key={event.id} direction="row" gap={2} alignItems="center">
        <Typography>{TeamEvent.Entity.getName(event)}</Typography>
        <Typography>
          {CustomDate.toDisplayTime(event.startDateTime)} -{" "}
          {CustomDate.toDisplayTime(event.endDateTime)}
        </Typography>
        <Chip
          label={t(eventTypeKeys[event.type])}
          color={event.type === TeamEvent.TeamEventType.Single ? "secondary" : "primary"}
        />
      </Stack>
      <Divider />
    </Stack>
  );
};

export default EventItem;
