import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Select,
  FormLabel,
  MenuItem,
  LocalError,
  LocalLoader,
  useGetAdminTeams,
  playersState,
  useOrganization,
  Stack,
} from "@mio/ui";
import { Player, PopulatedPlayer, TeamId } from "@mio/helpers";
import { PlayerAgeAlert } from "./playerAgeAlert";

type Props = {
  player: PopulatedPlayer;
  assignTeam: playersState.AssignToTeamHandler;
};

export const AssignToTeam: FC<Props> = ({ player, assignTeam }) => {
  const { t } = useTranslation();
  const [showDialog, toggleDialog] = useState(false);
  const [selectedTeamId, setSelectedTeamId] = useState<TeamId | "">("");
  const organization = useOrganization();

  const teams = useGetAdminTeams(organization.id);

  const closeDialog = () => {
    setSelectedTeamId("");
    toggleDialog(false);
  };

  const handleSubmit = () => {
    if (selectedTeamId) {
      assignTeam.mutate(
        { organizationId: organization.id, teamId: selectedTeamId, playerId: player.id },
        {
          onSuccess: closeDialog,
        },
      );
    }
  };

  const selectedTeam = selectedTeamId && teams.data?.find((t) => t.id === selectedTeamId);

  return (
    <>
      <Stack direction="row" spacing={2}>
        <Button onClick={() => toggleDialog(true)} variant="contained" color="secondary">
          {t("players.assign-to-team")}
        </Button>
      </Stack>

      <Dialog open={showDialog}>
        <DialogTitle id="confirmation-dialog-title" sx={{ mb: 2 }}>
          {t("players.assign-player-to-team", { name: Player.getFullName(player) } as any)}
        </DialogTitle>

        {teams.isLoading && <LocalLoader message={t("teams.loading")} />}

        {teams.isError && <LocalError message={t("teams.load-error")} />}

        {teams.isSuccess && (
          <>
            <DialogContent>
              <Box mb={2}>
                <Box mb={2}>
                  <FormLabel>{t("teams.select-team")}:</FormLabel>
                </Box>

                <Select<TeamId | "">
                  fullWidth
                  onChange={(event) => {
                    const teamId = event.target.value as TeamId;
                    setSelectedTeamId(teamId);
                  }}
                  value={selectedTeamId}
                >
                  {teams.data.map((team) => (
                    <MenuItem key={team.id} value={team.id}>
                      {team.name}
                    </MenuItem>
                  ))}
                </Select>

                {assignTeam.isError && (
                  <Box mt={2}>
                    <LocalError message={t("common.error")} />
                  </Box>
                )}

                {selectedTeam && (
                  <Box mt={2}>
                    <PlayerAgeAlert team={selectedTeam} player={player} />
                  </Box>
                )}
              </Box>
            </DialogContent>
            <DialogActions>
              <Button variant="outlined" onClick={closeDialog} sx={{ mr: 3 }}>
                {t("common.cancel")}
              </Button>
              <Button
                variant="contained"
                onClick={handleSubmit}
                disabled={!selectedTeamId}
                loading={assignTeam.isLoading}
              >
                {t("players.assign")}
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </>
  );
};
