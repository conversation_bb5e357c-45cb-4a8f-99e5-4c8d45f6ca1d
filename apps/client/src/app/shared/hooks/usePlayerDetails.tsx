import { FC, useState } from "react";
import { useCopyToClipboard } from "react-use";
import { startCase } from "lodash/fp";
import { useTranslation } from "react-i18next";

import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stack,
  Chip,
  Typography,
  Divider,
  MUILink,
  SendIcon,
  IconButton,
  ContentCopyIcon,
  DeleteIcon,
  useTheme,
  useMediaQuery,
  useOrganization,
  playersState,
  Tooltip,
} from "@mio/ui";
import { PopulatedPlayer, Player, CustomDate, TeamId } from "@mio/helpers";
import { usePlayerTeams } from "./usePlayerTeams";
import { useEmailModal } from "./useSendEmail";
import { useConfirmationModal } from "./useConfirmation";

const PlayerDetails: FC<{
  player: PopulatedPlayer;
  removeFromTeam: playersState.RemoveFromTeamHandler;
  onRemoveFromTeam: () => void;
  handleCloseModal: () => void;
}> = ({ player, removeFromTeam, onRemoveFromTeam, handleCloseModal }) => {
  const { t } = useTranslation();
  const organization = useOrganization();

  const [, copyToClipboard] = useCopyToClipboard();
  const profilesWithTeams = usePlayerTeams(player);

  const { SendEmailUI, show } = useEmailModal();

  const [selectedPlayerTeamId, setSelectedPlayerTeamId] = useState<undefined | TeamId>();

  const handleDelete = () => {
    if (selectedPlayerTeamId) {
      removeFromTeam.mutate(
        {
          teamId: selectedPlayerTeamId,
          organizationId: organization.id,
          playerId: player.id,
        },
        {
          onSuccess: () => {
            onRemoveFromTeam();
            removeFromTeam.reset();
          },
        },
      );
    }
  };

  const { mutate, isLoading, isError, isSuccess, reset } =
    playersState.useRemovePlayerFromOrganization();

  const {
    ConfirmationModalUI: RemoveFromOrgConfirmationModalUI,
    show: showRemoveFromOrgConfirmation,
  } = useConfirmationModal({
    isError: isError,
    isSuccess: isSuccess,
    isLoading: isLoading,
    title: t("players.details.remove-from-organization-dialog-title"),
    onConfirm: () =>
      player.id &&
      organization.id &&
      mutate(
        { playerId: player.id, organizationId: organization.id },
        {
          onSuccess: () => {
            handleCloseModal();
            reset();
          },
        },
      ),
    onCancel: () => reset(),
  });

  const { ConfirmationModalUI, show: showDeleteConfirmation } = useConfirmationModal({
    isError: removeFromTeam.isError,
    isSuccess: removeFromTeam.isSuccess,
    isLoading: removeFromTeam.isLoading,
    title: t("players.details.remove-from-team-dialog-title"),
    onConfirm: handleDelete,
  });

  return (
    <Stack gap={3}>
      <Divider />

      <Box>
        <Chip label={t("players.details.contact")} sx={{ mb: 2 }} color="secondary" />
        <Stack direction="row" gap={5} flexWrap="wrap">
          <Box>
            <Typography>{t("players.details.address")}</Typography>
            <Typography>{player.address.address}</Typography>
          </Box>

          <Box>
            <Typography>{t("players.details.postcode")}</Typography>
            <Typography>{player.address.postcode}</Typography>
          </Box>

          {player.phone && (
            <Box>
              <Typography>{t("players.details.phone")}</Typography>
              <MUILink href={`tel:${player.phone}`}>{player.phone}</MUILink>
            </Box>
          )}

          {player.email && (
            <Box>
              <Typography>{t("players.details.email")}</Typography>
              <Stack gap={2} direction="row" alignItems="center">
                <Typography>{player.email}</Typography>
                <Tooltip title={t("players.details.copy-email")}>
                  <IconButton
                    aria-label={t("players.details.copy-email")}
                    onClick={() => copyToClipboard(player.email || "")}
                    color="primary"
                  >
                    <ContentCopyIcon />
                  </IconButton>
                </Tooltip>

                <Tooltip title={t("players.details.send-email")}>
                  <IconButton
                    aria-label={t("players.details.send-email")}
                    onClick={() => show([player.email])}
                    color="primary"
                  >
                    <SendIcon />
                  </IconButton>
                </Tooltip>
              </Stack>
            </Box>
          )}
        </Stack>
      </Box>

      {player.guardian && (
        <Box>
          <Chip label={t("players.details.guardian")} sx={{ mb: 2 }} color="secondary" />
          <Divider />

          <Chip label="Guardian" sx={{ mb: 2 }} color="secondary" />
          <Stack direction="row" gap={5} flexWrap="wrap">
            <Box>
              <Typography>{t("players.details.name")}</Typography>
              <Typography>
                {player.guardian.firstName} {player.guardian.lastName}
              </Typography>
            </Box>

            <Box>
              <Typography>{t("players.details.born")}</Typography>
              <Typography>{CustomDate.toDisplayDate(player.guardian.dob)}</Typography>
            </Box>

            <Box>
              <Typography>{t("players.details.email")}</Typography>
              <Stack gap={2} direction="row" alignItems="center">
                <Typography>{player.guardian.email}</Typography>
                <Tooltip title={t("players.details.copy-email")}>
                  <IconButton
                    aria-label={t("players.details.copy-email")}
                    onClick={() => copyToClipboard(player.guardian?.email || "")}
                  >
                    <ContentCopyIcon />
                  </IconButton>
                </Tooltip>

                <Tooltip title={t("players.details.send-email")}>
                  <IconButton
                    aria-label={t("players.details.send-email")}
                    color="primary"
                    onClick={() => show([player.guardian?.email])}
                  >
                    <SendIcon />
                  </IconButton>
                </Tooltip>
              </Stack>
            </Box>

            <Box>
              <Typography>{t("players.details.phone")}</Typography>
              <MUILink href={`tel:${player.guardian.phone}`}>{player.guardian.phone}</MUILink>
            </Box>
          </Stack>
        </Box>
      )}

      <Divider />

      <>
        <Box>
          <Chip label={t("players.details.teams")} sx={{ mb: 2 }} color="secondary" />
          <Box>
            {profilesWithTeams.length > 0 ? (
              profilesWithTeams.map((item) => (
                <Stack
                  direction="row"
                  key={item.team.id}
                  justifyContent="space-between"
                  alignItems="center"
                >
                  <Stack direction="row" key={item.team.id}>
                    <Typography mr={2}>{item.team.name}: </Typography>
                    <Typography mr={2}>{startCase(item.profile.status)}</Typography>
                  </Stack>

                  <Tooltip title={t("players.details.remove-from-team")}>
                    <IconButton
                      aria-label={t("players.details.remove-from-team")}
                      color="primary"
                      onClick={() => {
                        setSelectedPlayerTeamId(item.team.id);
                        showDeleteConfirmation(t("players.details.remove-confirmation"));
                      }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </Stack>
              ))
            ) : (
              <Box>{t("players.details.no-teams")}</Box>
            )}
          </Box>
        </Box>

        <Divider />
      </>

      <Box>
        <Chip label={t("players.details.additional-info")} sx={{ mb: 2 }} color="secondary" />
        <Stack direction="row" gap={5} flexWrap="wrap">
          <Box>
            <Typography>{t("players.details.playing-experience")}</Typography>
            <Typography>{startCase(player.playingExperience)}</Typography>
            <Typography>{player.playingExperienceDescription}</Typography>
          </Box>

          <Box>
            <Typography>{t("players.details.medical-conditions")}</Typography>
            <Typography>{player.medicalConditions || t("players.details.none")}</Typography>
          </Box>

          <Box>
            <Typography>{t("players.details.preferred-position")}</Typography>
            <Typography>
              {player.preferredPlayingPosition || t("players.details.not-available")}
            </Typography>
          </Box>

          <SendEmailUI />
          <ConfirmationModalUI />
        </Stack>
      </Box>

      <Divider />

      <Box>
        <Button
          color="error"
          variant="contained"
          onClick={() =>
            showRemoveFromOrgConfirmation(
              t("players.details.remove-from-organization-confirmation"),
            )
          }
        >
          {t("players.details.remove-from-organization")}
        </Button>
      </Box>

      <RemoveFromOrgConfirmationModalUI />
    </Stack>
  );
};

type Props = {
  player: PopulatedPlayer;
  removeFromTeam: playersState.RemoveFromTeamHandler;
  onClose: () => void;
};

const PlayerDetailsModal: FC<Props> = ({ player, onClose, removeFromTeam }) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const isOnSmallScreen = useMediaQuery(theme.breakpoints.down("md"));

  return (
    <Dialog open={!!player} fullWidth={!isOnSmallScreen} fullScreen={isOnSmallScreen}>
      <DialogTitle id="confirmation-dialog-title" sx={{ mb: 2 }}>
        {t("players.details.title", { name: Player.getFullName(player) })}
      </DialogTitle>

      <DialogContent>
        <PlayerDetails
          player={player}
          removeFromTeam={removeFromTeam}
          onRemoveFromTeam={onClose}
          handleCloseModal={onClose}
        />
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" onClick={onClose}>
          {t("players.details.close")}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export const usePlayerDetails = (params: {
  removeFromTeam: playersState.RemoveFromTeamHandler;
}) => {
  const [player, setPlayer] = useState<PopulatedPlayer | undefined>();

  return {
    PlayerDetailsUI: () =>
      player ? (
        <PlayerDetailsModal
          player={player}
          removeFromTeam={params.removeFromTeam}
          onClose={() => setPlayer(undefined)}
        />
      ) : null,
    show: (player: PopulatedPlayer) => setPlayer(player),
  };
};
