import { FC } from "react";
import { Controller, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import {
  Box,
  Stack,
  LocalError,
  Select,
  MenuItem,
  Button,
  FormControl,
  useFormResolver,
  InputLabel,
} from "@mio/ui";
import {
  APIError,
  PlayerPerformanceBasicRatings,
  TrainingSessionPlayerReview,
  Attendance,
  PlayerAttributes,
  TrainingSessionReview,
  PlayerId,
} from "@mio/helpers";

import { PlayerReviewDataShape } from "./types";

type Props = {
  parentReview: TrainingSessionReview.Review;
  playerId: PlayerId;
  onSubmit: (data: PlayerReviewDataShape) => void;
  review?: TrainingSessionPlayerReview.PlayerReview;
  loading?: boolean;
  serverError?: APIError | null;
};

export const UpsertPlayerReviewForm: FC<Props> = ({
  review,
  onSubmit,
  playerId,
  loading,
  serverError,
  parentReview,
}) => {
  const { t } = useTranslation();
  const formResolver = useFormResolver<PlayerReviewDataShape>(
    TrainingSessionPlayerReview.Entity.toUpsertDto,
  );

  const { handleSubmit, control } = useForm<PlayerReviewDataShape>({
    resolver: formResolver,
    defaultValues: review
      ? { ...review }
      : {
          teamId: parentReview.teamId,
          organizationId: parentReview.organizationId,
          playerId,
          trainingSessionReviewId: parentReview.id,
          highlights: [],
        },
  });

  return (
    <Stack gap={3} mt={2} component="form" onSubmit={handleSubmit(onSubmit)}>
      <Controller
        name="attendance"
        control={control}
        render={(params) => (
          <FormControl>
            <InputLabel htmlFor="attendance-select">{t("training.attendance")}</InputLabel>

            <Select
              fullWidth
              inputProps={{ ...params.field }}
              id="attendance-select"
              label={t("training.attendance")}
            >
              <MenuItem value={Attendance.Attended}>
                {t("training.attendance-types.attended")}
              </MenuItem>
              <MenuItem value={Attendance.Late}>{t("training.attendance-types.late")}</MenuItem>
              <MenuItem value={Attendance.MissingWithReason}>
                {t("training.attendance-types.missing-with-reason")}
              </MenuItem>
              <MenuItem value={Attendance.MissingNoReason}>
                {t("training.attendance-types.missing-no-reason")}
              </MenuItem>
            </Select>
          </FormControl>
        )}
      />

      <Controller
        name="rating"
        control={control}
        render={(params) => (
          <FormControl>
            <InputLabel htmlFor="rating-select">{t("training.rating")}</InputLabel>

            <Select
              fullWidth
              inputProps={{ ...params.field }}
              id="rating-select"
              label={t("training.rating")}
            >
              <MenuItem value={PlayerPerformanceBasicRatings.Abysmal}>
                {t("training.ratings.abysmal")}
              </MenuItem>
              <MenuItem value={PlayerPerformanceBasicRatings.Bad}>
                {t("training.ratings.bad")}
              </MenuItem>
              <MenuItem value={PlayerPerformanceBasicRatings.Neutral}>
                {t("training.ratings.neutral")}
              </MenuItem>
              <MenuItem value={PlayerPerformanceBasicRatings.Good}>
                {t("training.ratings.good")}
              </MenuItem>
              <MenuItem value={PlayerPerformanceBasicRatings.Outstanding}>
                {t("training.ratings.outstanding")}
              </MenuItem>
            </Select>
          </FormControl>
        )}
      />

      <Controller
        name="highlights"
        control={control}
        render={(params) => (
          <FormControl>
            <InputLabel htmlFor="highlights-select">{t("training.highlights")}</InputLabel>

            <Select
              fullWidth
              inputProps={{ ...params.field }}
              id="highlights-select"
              label={t("training.highlights")}
              multiple
            >
              <MenuItem value={PlayerAttributes.Acceleration}>
                {t("training.attributes.acceleration")}
              </MenuItem>
              <MenuItem value={PlayerAttributes.Adaptability}>
                {t("training.attributes.adaptability")}
              </MenuItem>
              <MenuItem value={PlayerAttributes.Aggression}>
                {t("training.attributes.aggression")}
              </MenuItem>
              <MenuItem value={PlayerAttributes.Agility}>
                {t("training.attributes.agility")}
              </MenuItem>
              <MenuItem value={PlayerAttributes.Ambition}>
                {t("training.attributes.ambition")}
              </MenuItem>
              <MenuItem value={PlayerAttributes.Anticipation}>
                {t("training.attributes.anticipation")}
              </MenuItem>
            </Select>
          </FormControl>
        )}
      />

      <Box gap={2} sx={{ display: "flex", justifyContent: "center" }}>
        <Button type="submit" variant="contained" loading={loading}>
          {t("common.save")}
        </Button>
      </Box>

      {serverError && <LocalError message={t("common.error")} />}
    </Stack>
  );
};
