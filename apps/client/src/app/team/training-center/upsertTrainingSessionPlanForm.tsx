import { FC, useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import {
  Box,
  Typography,
  TextField,
  Stack,
  LocalError,
  Button,
  useOrganizationId,
  useFormResolver,
  DateTimePicker,
  useRequiredTeamId,
} from "@mio/ui";

import { APIError, CustomDate, ErrorMessages, TrainingSessionPlan, Primitive } from "@mio/helpers";

import { BackButton } from "../../shared";
import { usePracticeUpload } from "./useTrainingSessionPractice";
// import { useDrillsModal } from "./drills/useDrills";

type DataShape = Partial<Primitive<TrainingSessionPlan.CreateDto> & { id?: string }>;

type Props = {
  trainingSessionPlan?: TrainingSessionPlan.CompletePlan;
  onSubmit: (data: DataShape) => void;
  loading?: boolean;
  serverError?: APIError | null;
};

export const UpsertTrainingSessionPlan: FC<Props> = ({
  trainingSessionPlan,
  onSubmit,
  loading,
  serverError,
}) => {
  const { t } = useTranslation();
  const organizationId = useOrganizationId();
  const teamId = useRequiredTeamId();
  const formResolver = useFormResolver(TrainingSessionPlan.Entity.toUpsertDto);

  const [showAddPracticeOptions, setShowAddPracticeOptions] = useState(false);

  const { register, handleSubmit, setValue, watch, formState } = useForm<DataShape>({
    defaultValues: {
      arrivalAndWarmUp: trainingSessionPlan?.arrivalAndWarmUp,
      coachBehaviours: trainingSessionPlan?.coachBehaviours,
      endDateTime: trainingSessionPlan?.endDateTime,
      expectedAttendees: trainingSessionPlan?.expectedAttendees,
      fourCornerPlayerConsiderations: trainingSessionPlan?.fourCornerPlayerConsiderations,
      location: trainingSessionPlan?.location,
      name: trainingSessionPlan?.name,
      sessionNumber: trainingSessionPlan?.sessionNumber,
      startDateTime: trainingSessionPlan?.startDateTime,

      learningOutcomes: trainingSessionPlan?.learningOutcomes,

      playersEngagement: trainingSessionPlan?.playersEngagement,
      safetyConsiderations: trainingSessionPlan?.safetyConsiderations,

      organizationId: organizationId,
      teamId: teamId,
      id: trainingSessionPlan?.id,
    },
    resolver: formResolver,
  });

  const { PracticeUploadUI, show: showPracticeUpload } = usePracticeUpload();

  //   const handleSelectDrill = (drill: TrainingSessionDrill.Drill) => {
  //     setValue("drillId", drill.id);
  //   };

  //   const drills = useDrillsModal({ handleSelectDrill });

  const { errors, submitCount } = formState;
  const hasSubmitted = submitCount > 0;

  const title = trainingSessionPlan
    ? t("matches.form.edit-title", { name: trainingSessionPlan.name })
    : t("matches.form.create-title");

  return (
    <Box
      width={1}
      component="form"
      onSubmit={handleSubmit(onSubmit)}
      sx={{ width: "100%", bgcolor: "background.paper" }}
    >
      <Stack direction="row" alignItems="center" mb={5} gap={1} flexWrap="wrap">
        <Box>
          <BackButton path="../">{t("matches.form.back-to-match-center")}</BackButton>
        </Box>
        <Box component="legend" display="block" sx={{ flexGrow: 1, pr: 5 }}>
          <Typography variant="h5" component="h1" textAlign="center">
            {title}
          </Typography>
        </Box>
      </Stack>

      <Box>
        <TextField
          fullWidth
          aria-required
          id="name"
          label={t("matches.form.fields.name")}
          name="name"
          inputProps={{ ...register("name") }}
          helperText={(hasSubmitted && errors.name?.message) || ""}
          error={hasSubmitted && !!errors.name?.message}
          aria-invalid={hasSubmitted && !!errors.name?.message}
        />
      </Box>

      <Box mt={2}>
        <DateTimePicker
          label={t("matches.form.fields.start-date")}
          format="dd/MM/y HH:mm"
          ampm={false}
          onChange={(newValue) => {
            setValue("startDateTime", CustomDate.validOrEmpty(newValue), {
              shouldValidate: true,
              shouldDirty: true,
              shouldTouch: true,
            });
          }}
          value={watch("startDateTime")}
          slotProps={{
            textField: {
              fullWidth: true,
              "aria-required": true,
              helperText: (hasSubmitted && errors.startDateTime?.message) || "",
              error: hasSubmitted && !!errors.startDateTime?.message,
              "aria-invalid": hasSubmitted && !!errors.startDateTime?.message,
            },
          }}
        />
      </Box>

      <Box mt={2}>
        <Button
          variant="contained"
          color="secondary"
          onClick={() => setShowAddPracticeOptions(true)}
        >
          {/* {t("training.form.add-practice-options")} */}
          {t("training.add-practice")}
        </Button>
      </Box>

      {showAddPracticeOptions && (
        <Stack direction="row" gap={2} mt={2}>
          <Button variant="contained" color="secondary">
            {t("training.select-from-library")}
          </Button>

          <Button variant="contained" color="secondary" onClick={showPracticeUpload}>
            {t("training.upload-new")}
          </Button>
        </Stack>
      )}

      {/* <Box mt={2}>
        <TextField
          fullWidth
          id="location"
          label={t("training.form.fields.location")}
          name="location"
          inputProps={{ ...register("location") }}
          helperText={(hasSubmitted && errors.location?.message) || ""}
          error={hasSubmitted && !!errors.location?.message}
          aria-invalid={hasSubmitted && !!errors.location?.message}
        />
      </Box> */}

      {/* <Box mt={2}>
        <TextField
          fullWidth
          id="learningOutcomes"
          label={t("matches.form.fields.learning-outcomes")}
          name="learningOutcomes"
          multiline
          rows={3}
          inputProps={{ ...register("learningOutcomes") }}
          helperText={(hasSubmitted && errors.learningOutcomes?.message) || ""}
          error={hasSubmitted && !!errors.learningOutcomes?.message}
          aria-invalid={hasSubmitted && !!errors.learningOutcomes?.message}
        />
      </Box>

      <Box mt={2}>
        <TextField
          fullWidth
          id="playersEngagement"
          label={t("matches.form.fields.players-engagement")}
          name="playersEngagement"
          multiline
          rows={3}
          inputProps={{ ...register("playersEngagement") }}
          helperText={(hasSubmitted && errors.playersEngagement?.message) || ""}
          error={hasSubmitted && !!errors.playersEngagement?.message}
          aria-invalid={hasSubmitted && !!errors.playersEngagement?.message}
        />
      </Box>

      <Box mt={2}>
        <TextField
          fullWidth
          id="coachBehaviours"
          label={t("matches.form.fields.coach-behaviours")}
          name="coachBehaviours"
          multiline
          rows={3}
          inputProps={{ ...register("coachBehaviours") }}
          helperText={(hasSubmitted && errors.coachBehaviours?.message) || ""}
          error={hasSubmitted && !!errors.coachBehaviours?.message}
          aria-invalid={hasSubmitted && !!errors.coachBehaviours?.message}
        />
      </Box>

      <Box mt={2}>
        <TextField
          fullWidth
          id="safetyConsiderations"
          label={t("matches.form.fields.safety-considerations")}
          name="safetyConsiderations"
          multiline
          rows={3}
          inputProps={{ ...register("safetyConsiderations") }}
          helperText={(hasSubmitted && errors.safetyConsiderations?.message) || ""}
          error={hasSubmitted && !!errors.safetyConsiderations?.message}
          aria-invalid={hasSubmitted && !!errors.safetyConsiderations?.message}
        />
      </Box>

      <Box mt={2}>
        <TextField
          fullWidth
          id="arrivalAndWarmUp"
          label={t("matches.form.fields.arrival-and-warm-up")}
          name="arrivalAndWarmUp"
          multiline
          rows={3}
          inputProps={{ ...register("arrivalAndWarmUp") }}
          helperText={(hasSubmitted && errors.arrivalAndWarmUp?.message) || ""}
          error={hasSubmitted && !!errors.arrivalAndWarmUp?.message}
          aria-invalid={hasSubmitted && !!errors.arrivalAndWarmUp?.message}
        />
      </Box> */}

      <Box mt={5} mb={2} sx={{ display: "flex", justifyContent: "center" }}>
        <Button size="large" type="submit" loading={loading} variant="contained" color="secondary">
          {t("matches.form.save")}
        </Button>
      </Box>

      {serverError &&
        (serverError.message === ErrorMessages.MatchAlreadyExistsInGameWeek ? (
          <LocalError message={t("matches.form.match-already-exists-in-game-week-error")} />
        ) : (
          <LocalError message={t("matches.form.error")} />
        ))}

      <PracticeUploadUI />
    </Box>
  );
};
