import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useTheme,
  useMediaQuery,
  useOrganization,
  useUploadCoachImage,
  useProvidedCurrentUser,
  ImageUpload,
} from "@mio/ui";
import { Assets } from "@mio/helpers";

type Props = {
  onClose: () => void;
  showModal: boolean;
};

const TrainingSessionPracticeModal: FC<Props> = ({ onClose, showModal }) => {
  const { t } = useTranslation();

  const theme = useTheme();
  const organization = useOrganization();
  const coach = useProvidedCurrentUser();
  const isOnSmallScreen = useMediaQuery(theme.breakpoints.down("md"));

  const uploadImage = useUploadCoachImage();

  const handleUpload = (file: Assets.Image.ParsedFile) => {
    console.log("useTrainingSessionPracticeModal", file.type);
    uploadImage.mutate(
      {
        dto: { file },
        coachId: coach.id,
        organizationId: organization.id,
      },
      {
        onSuccess: (data) => {
          console.log("data returned", data);
        },
      },
    );

    onClose();
  };

  return (
    <Dialog open={showModal} fullWidth={!isOnSmallScreen} fullScreen={isOnSmallScreen}>
      <DialogTitle id="confirmation-dialog-title" sx={{ mb: 2 }}>
        {t("email.send.title")}
      </DialogTitle>

      <DialogContent>
        <ImageUpload
          onUpload={handleUpload}
          isLoading={uploadImage.isLoading}
          error={uploadImage.error || undefined}
          success={uploadImage.isSuccess}
        />
      </DialogContent>
      <DialogActions></DialogActions>
    </Dialog>
  );
};

export const usePracticeUpload = () => {
  const [showModal, setShowModal] = useState<boolean>(false);

  return {
    PracticeUploadUI: () =>
      showModal ? (
        <TrainingSessionPracticeModal onClose={() => setShowModal(false)} showModal={showModal} />
      ) : null,
    show: () => setShowModal(true),
  };
};
