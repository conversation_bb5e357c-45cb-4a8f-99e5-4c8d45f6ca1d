import { produce } from "immer";

import {
  Attendance,
  LeanPlayer,
  PlayerId,
  PlayerPerformanceBasicRatings,
  TrainingSessionPlayerReview,
} from "@mio/helpers";

type PlayerWithStats = {
  player: LeanPlayer;
  reviews: number;
  attendance: {
    [Attendance.Attended]: number;
    [Attendance.Late]: number;
    [Attendance.MissingWithReason]: number;
    [Attendance.MissingNoReason]: number;
  };
  performance: {
    [PlayerPerformanceBasicRatings.Good]: number;
    [PlayerPerformanceBasicRatings.Bad]: number;
    [PlayerPerformanceBasicRatings.Neutral]: number;
    [PlayerPerformanceBasicRatings.Abysmal]: number;
    [PlayerPerformanceBasicRatings.Outstanding]: number;
  };
};

// const getPlayersWithStats = (
//   reviews: TrainingSessionPlayerReview.ExtendedReview[],
// ): PlayerWithStats[] => {
//   const data = reviews.reduce((acc, review) => {
//     const existingRecord = acc[review.playerId] || {
//       player: review.player,
//       reviews: 0,
//       attendance: {
//         [Attendance.Attended]: 0,
//         [Attendance.Late]: 0,
//         [Attendance.MissingWithReason]: 0,
//         [Attendance.MissingNoReason]: 0,
//       },
//       performance: {
//         [PlayerPerformanceBasicRatings.Good]: 0,
//         [PlayerPerformanceBasicRatings.Bad]: 0,
//         [PlayerPerformanceBasicRatings.Neutral]: 0,
//         [PlayerPerformanceBasicRatings.Abysmal]: 0,
//         [PlayerPerformanceBasicRatings.Outstanding]: 0,
//       },
//     };

//     const newRecord = { ...existingRecord };

//     newRecord.reviews += 1;
//     newRecord.attendance[review.attendance] += 1;
//     newRecord.performance[review.rating] += 1;

//     acc[review.playerId] = newRecord;

//     return acc;
//   }, {} as Record<PlayerId, PlayerWithStats>);

//   return Object.values(data);
// };

type PlayerWithTotals = Pick<PlayerWithStats, "player" | "reviews"> & {
  attendance: number;
  performance: number;
};

export enum SortingOrder {
  Ascending = "asc",
  Descending = "desc",
}

export enum SortingOption {
  Name,
  Reviews,
  Attendance,
  Performance,
}

export const getPlayerTotals = (
  payload: TrainingSessionPlayerReview.ExtendedReview[],
  sortingOption = SortingOption.Reviews,
  order = SortingOrder.Descending,
): PlayerWithTotals[] => {
  const data = payload.reduce((acc, review) => {
    const existingRecord = acc[review.playerId] || {
      player: review.player,
      reviews: 0,
      attendance: 0,
      performance: 0,
    };

    const newRecord = produce(existingRecord, (draft) => {
      draft.reviews += 1;
      draft.attendance += attendancePointsMapping[review.attendance];
      draft.performance += performancePointsMapping[review.rating];
    });

    acc[review.playerId] = newRecord;

    return acc;
  }, {} as Record<PlayerId, PlayerWithTotals>);

  const withTotals = Object.values(data).map((_record) => {
    const record = produce(_record, (draft) => {
      const maxPossibleRating =
        draft.reviews * performancePointsMapping[PlayerPerformanceBasicRatings.Outstanding];

      const maxPossibleAttendance = draft.reviews * attendancePointsMapping[Attendance.Attended];

      if (maxPossibleRating !== 0) {
        draft.performance = (draft.performance / maxPossibleRating) * 100;
      } else {
        draft.performance = 0;
      }

      if (maxPossibleAttendance !== 0) {
        draft.attendance = (draft.attendance / maxPossibleAttendance) * 100;
      } else {
        draft.attendance = 0;
      }
    });

    return record;
  });

  return withTotals.sort((a, b) => {
    switch (sortingOption) {
      case SortingOption.Reviews:
        return order === SortingOrder.Descending ? b.reviews - a.reviews : a.reviews - b.reviews;
      case SortingOption.Attendance:
        return order === SortingOrder.Descending
          ? b.attendance - a.attendance
          : a.attendance - b.attendance;
      case SortingOption.Performance:
        return order === SortingOrder.Descending
          ? b.performance - a.performance
          : a.performance - b.performance;
      case SortingOption.Name:
        return order === SortingOrder.Descending
          ? b.player.firstName.localeCompare(a.player.firstName)
          : a.player.firstName.localeCompare(b.player.firstName);
      default:
        return 0;
    }
  });
};

export const getTotalNumberOfPlayers = (reviews: TrainingSessionPlayerReview.ExtendedReview[]) => {
  const total = reviews.reduce((acc, review) => {
    if (!acc.has(review.playerId)) {
      acc.add(review.playerId);
    }

    return acc;
  }, new Set());

  return total.size;
};

const performancePointsMapping = {
  [PlayerPerformanceBasicRatings.Outstanding]: 5,
  [PlayerPerformanceBasicRatings.Good]: 4,
  [PlayerPerformanceBasicRatings.Neutral]: 3,
  [PlayerPerformanceBasicRatings.Bad]: 2,
  [PlayerPerformanceBasicRatings.Abysmal]: 1,
} as const;

export const getTotalPerformance = (ratings: PlayerPerformanceBasicRatings[]) => {
  const maxResult =
    ratings.length * performancePointsMapping[PlayerPerformanceBasicRatings.Outstanding];

  const actualResult = ratings.reduce((acc, rating) => {
    acc += performancePointsMapping[rating];
    return acc;
  }, 0);

  return (actualResult / maxResult) * 100;
};

const attendancePointsMapping = {
  [Attendance.Attended]: 1,
  [Attendance.Late]: 1,
  [Attendance.MissingWithReason]: 0,
  [Attendance.MissingNoReason]: 0,
} as const;

export const getTotalAttendance = (attendance: Attendance[]) => {
  const maxResult = attendance.length * attendancePointsMapping[Attendance.Attended];

  const actualResult = attendance.reduce((acc, rating) => {
    acc += attendancePointsMapping[rating];
    return acc;
  }, 0);

  return (actualResult / maxResult) * 100;
};

export const formatPercent = (percent: number) => {
  return `${percent.toFixed(0)}%`;
};

export const percentToRatingFormatted = (percentResult: number, maxRating = 5) => {
  const scaled = (percentResult / 100) * maxRating;

  if (scaled % 1 === 0) {
    return `${scaled.toFixed(0)} / ${maxRating} `;
  }

  return `${parseFloat(scaled.toFixed(2))} / ${maxRating} `;
};

export const percentToRating = (percentResult: number, maxRating = 5) => {
  const scaled = (percentResult / 100) * maxRating;

  return scaled;
};
