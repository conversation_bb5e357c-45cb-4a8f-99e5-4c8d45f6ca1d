import { FC } from "react";
import { useTranslation } from "react-i18next";
import { DataGrid, GridColDef } from "@mui/x-data-grid";

import {
  Box,
  Divider,
  footballMatchStatsState,
  PageError,
  PageLoader,
  useOrganization,
  useRequiredTeamId,
  Paper,
} from "@mio/ui";
import { ErrorMessages } from "@mio/helpers";

export const GoalsStats: FC = () => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const teamId = useRequiredTeamId();

  const columns: GridColDef[] = [
    { field: "firstName", headerName: t("stats.first-name"), flex: 1 },
    { field: "lastName", headerName: t("stats.last-name"), flex: 1 },
    {
      field: "appearances",
      headerName: t("stats.appearances"),
      width: 200,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "stat-per-match",
      headerName: t("stats.goals-per-match"),
      type: "number",
      align: "center",
      headerAlign: "center",
      width: 200,
      valueFormatter: (value: number) => value?.toFixed(2),
      renderCell: (params) => (
        <Box
          sx={{
            width: "100%",
            height: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            color: params.value >= 0.5 ? "#fff" : "inherit",
            backgroundColor: params.value >= 0.5 ? "#82d094" : "inherit",
          }}
        >
          {params.value?.toFixed(2)}
        </Box>
      ),
    },
    {
      field: "stat",
      headerName: t("stats.goals"),
      type: "number",
      align: "center",
      headerAlign: "center",
      width: 200,
    },
  ];

  const goalsStatsQuery = footballMatchStatsState.useGetMatchGoalsStats(organization.id, teamId);

  if (goalsStatsQuery.status === "loading") {
    return <PageLoader message={t("stats.loading")} />;
  }

  if (goalsStatsQuery.status === "error") {
    return (
      <Box>
        <PageError
          message={
            goalsStatsQuery?.error?.message === ErrorMessages.PermissionDenied
              ? t("common.permission-denied")
              : t("stats.load-error")
          }
        />
      </Box>
    );
  }

  if (goalsStatsQuery.status === "success") {
    return (
      <Paper sx={{ width: "100%" }}>
        <DataGrid
          getRowId={(row) => row.playerTeamProfileId}
          rows={goalsStatsQuery.data}
          columns={columns}
          initialState={{
            sorting: {
              sortModel: [
                { field: "stat", sort: "desc" },
                { field: "appearances", sort: "asc" },
              ],
            },
          }}
          sx={{ border: 10, borderColor: "white" }}
        />

        <Divider />
      </Paper>
    );
  }

  return null;
};
