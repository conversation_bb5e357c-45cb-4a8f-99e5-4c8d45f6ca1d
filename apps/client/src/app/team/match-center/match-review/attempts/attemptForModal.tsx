import { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  footballMatchState,
  Typography,
  useOrganization,
  useRequiredTeamId,
} from "@mio/ui";

import { useTranslation } from "react-i18next";
import { AttemptForForm } from "../goals-review/scored-goals/goalScoredForm";
import { useTeamPlayers } from "@mio/ui/lib/state/usePlayers";
import { FootballMatchAttemptFor, isError } from "@mio/helpers";

type Props = {
  attemptFor?: FootballMatchAttemptFor.AttemptFor;
};

export const NoGoalsAttemptForForm = ({ attemptFor, onClose }: Props & { onClose: () => void }) => {
  const { t } = useTranslation();
  const teamId = useRequiredTeamId();
  const organization = useOrganization();

  const teamPlayers = useTeamPlayers(teamId, organization.id);

  const updateAttemptForReview = footballMatchState.useUpdateFootballMatchAttemptFor(
    organization.id,
    teamId,
  );

  const createAttemptForReview = footballMatchState.useCreateFootballMatchAttemptFor(
    organization.id,
    teamId,
  );

  const handleSubmit = (data: unknown) => {
    if (typeof data === "object" && data && "id" in data && data.id) {
      const validated = FootballMatchAttemptFor.Entity.toUpdateDto(data);

      if (!isError(validated)) {
        updateAttemptForReview.mutate(validated);
      }
    } else {
      const validated = FootballMatchAttemptFor.Entity.toCreateDto(data);

      if (!isError(validated)) {
        createAttemptForReview.mutate(validated);
      }
    }

    onClose();
  };

  return (
    teamPlayers.query.data &&
    teamPlayers.query.data.length > 0 && (
      <Dialog open={true} fullWidth onClose={onClose} maxWidth="lg">
        <DialogTitle>
          <Typography variant="h5" component="h2">
            {t("match.attempt")}
          </Typography>
        </DialogTitle>
        <DialogContent>
          <AttemptForForm
            index={0}
            onSubmit={handleSubmit}
            attemptFor={attemptFor}
            upsertReviewIsSuccess={
              attemptFor && "id" in attemptFor && attemptFor.id
                ? updateAttemptForReview.isSuccess
                : createAttemptForReview.isSuccess
            }
            teamPlayers={teamPlayers.query.data ?? []}
          />
        </DialogContent>
      </Dialog>
    )
  );
};

export const NoGoalsAttemptForFormModal = ({
  isOpen,
  onClose,
  attemptFor,
}: {
  isOpen: boolean;
  onClose: () => void;
  attemptFor?: FootballMatchAttemptFor.AttemptFor;
}) => {
  if (!isOpen) return null;
  return <NoGoalsAttemptForForm attemptFor={attemptFor} onClose={onClose} />;
};

export const useNoGoalsAttemptForForm = () => {
  const [attempt, setAttempt] = useState<FootballMatchAttemptFor.AttemptFor | undefined>();
  const [isOpen, setIsOpen] = useState(false);

  return {
    isOpen,
    attempt,
    toggle: () => setIsOpen(!isOpen),
    setAttempt: (attemptFor: FootballMatchAttemptFor.AttemptFor | undefined) =>
      setAttempt(attemptFor),
  };
};
