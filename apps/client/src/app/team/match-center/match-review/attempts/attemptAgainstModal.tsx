import { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  footballMatchState,
  Typography,
  useOrganization,
  useRequiredTeamId,
} from "@mio/ui";

import { useTranslation } from "react-i18next";
import { AttemptAgainstForm } from "../goals-review/conceded-goals/goalConcededForm";
import { useTeamPlayers } from "@mio/ui/lib/state/usePlayers";
import { FootballMatchAttemptAgainst, isError } from "@mio/helpers";

type Props = {
  attemptAgainst?: FootballMatchAttemptAgainst.AttemptAgainst;
};

export const NoGoalsAttemptAgainstForm = ({
  attemptAgainst,
  onClose,
}: Props & { onClose: () => void }) => {
  const { t } = useTranslation();
  const teamId = useRequiredTeamId();
  const organization = useOrganization();

  const teamPlayers = useTeamPlayers(teamId, organization.id);

  const updateAttemptAgainstReview = footballMatchState.useUpdateFootballMatchAttemptAgainst(
    organization.id,
    teamId,
  );

  const createAttemptAgainstReview = footballMatchState.useCreateFootballMatchAttemptAgainst(
    organization.id,
    teamId,
  );

  const handleSubmit = (data: unknown) => {
    if (typeof data === "object" && data && "id" in data && data.id) {
      const validated = FootballMatchAttemptAgainst.Entity.toUpdateDto(data);

      if (!isError(validated)) {
        updateAttemptAgainstReview.mutate(validated);
      }
    } else {
      const validated = FootballMatchAttemptAgainst.Entity.toCreateDto(data);

      if (!isError(validated)) {
        createAttemptAgainstReview.mutate(validated);
      }
    }

    onClose();
  };

  return (
    teamPlayers.query.data &&
    teamPlayers.query.data.length > 0 && (
      <Dialog open={true} fullWidth onClose={onClose} maxWidth="lg">
        <DialogTitle>
          <Typography variant="h5" component="h2">
            {t("match.attempt")}
          </Typography>
        </DialogTitle>
        <DialogContent>
          <AttemptAgainstForm
            index={0}
            onSubmit={handleSubmit}
            attemptAgainst={attemptAgainst}
            upsertReviewIsSuccess={
              attemptAgainst && "id" in attemptAgainst && attemptAgainst.id
                ? updateAttemptAgainstReview.isSuccess
                : createAttemptAgainstReview.isSuccess
            }
            teamPlayers={teamPlayers.query.data ?? []}
          />
        </DialogContent>
      </Dialog>
    )
  );
};

export const NoGoalsAttemptAgainstFormModal = ({
  isOpen,
  onClose,
  attemptAgainst,
}: {
  isOpen: boolean;
  onClose: () => void;
  attemptAgainst?: FootballMatchAttemptAgainst.AttemptAgainst;
}) => {
  if (!isOpen) return null;
  return <NoGoalsAttemptAgainstForm attemptAgainst={attemptAgainst} onClose={onClose} />;
};

export const useNoGoalsAttemptAgainstForm = () => {
  const [attempt, setAttempt] = useState<FootballMatchAttemptAgainst.AttemptAgainst | undefined>();
  const [isOpen, setIsOpen] = useState(false);

  return {
    isOpen,
    attempt,
    toggle: () => setIsOpen(!isOpen),
    setAttempt: (attemptAgainst: FootballMatchAttemptAgainst.AttemptAgainst | undefined) =>
      setAttempt(attemptAgainst),
  };
};
