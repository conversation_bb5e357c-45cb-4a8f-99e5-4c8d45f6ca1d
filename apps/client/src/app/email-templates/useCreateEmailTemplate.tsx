import { FC, useState } from "react";
import { useTranslation } from "react-i18next";
import { EmailTemplate, isError } from "@mio/helpers";
import { Modal, Typography, useCreateEmailTemplate } from "@mio/ui";
import { UpsertEmailTemplate } from "./upsert.form";

type Props = {
  onSuccess: () => void;
};

const CreateEmailTemplate: FC<Props> = ({ onSuccess }) => {
  const createEmailTemplate = useCreateEmailTemplate();
  const [error, setError] = useState<undefined | boolean>(undefined);

  const handleSubmit = (data: unknown) => {
    const validated = EmailTemplate.toCreateDto(data);
    if (isError(validated)) {
      setError(true);
    } else {
      createEmailTemplate.mutate(validated, {
        onSuccess,
      });
    }
  };

  return (
    <UpsertEmailTemplate
      onSubmit={handleSubmit}
      loading={createEmailTemplate.isLoading}
      error={!!createEmailTemplate.error || error}
    />
  );
};

export const useCreateEmailTemplateUI = () => {
  const { t } = useTranslation();
  const [isActive, setActive] = useState(false);

  return {
    createEmailTemplate: () => setActive(true),
    CreateUI: (
      <Modal
        open={isActive}
        fullWidth
        onClose={() => setActive(false)}
        title={
          <Typography variant="h5" component="h2">
            {t("email-templates.create-template")}
          </Typography>
        }
        content={<CreateEmailTemplate onSuccess={() => setActive(false)} />}
      />
    ),
  };
};
