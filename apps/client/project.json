{"name": "client", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/client/src", "projectType": "application", "tags": [], "targets": {"type-check": {"executor": "nx:run-commands", "options": {"command": "npx tsc -b ./apps/client"}}, "build": {"executor": "@nx/vite:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "dist/apps/client", "configFile": "apps/client/vite.config.ts"}, "configurations": {"development": {"mode": "development"}, "production": {"mode": "production", "fileReplacements": [{"replace": "apps/client/src/environments/environment.ts", "with": "apps/client/src/environments/environment.prod.ts"}]}, "demo": {"mode": "demo", "fileReplacements": [{"replace": "apps/client/src/environments/environment.ts", "with": "apps/client/src/environments/environment.demo.ts"}]}}}, "serve": {"executor": "@nx/vite:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "client:build"}, "configurations": {"development": {"buildTarget": "client:build:development", "hmr": true}, "production": {"buildTarget": "client:build:production", "hmr": false}, "demo": {"buildTarget": "client:build:demo"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/vite:test", "outputs": ["{options.reportsDirectory}"], "options": {"reportsDirectory": "../../coverage/apps/client", "configFile": "apps/client/vitest.config.ts"}}}}