import { mergeConfig } from "vite";
import { defineConfig } from "vitest/config";
import viteConfig from "./vite.config";

export default mergeConfig(
  viteConfig,
  defineConfig({
    test: {
      passWithNoTests: true,
      watch: false,
      globals: true,
      environment: "jsdom",
      include: ["src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}"],
      reporters: ["default"],
      setupFiles: ["./src/test-setup.ts"],
      coverage: {
        reportsDirectory: "../../coverage/apps/client",
        provider: "v8",
      },
      alias: {
        crypto: "node:crypto",
      },
    },
  }),
);
