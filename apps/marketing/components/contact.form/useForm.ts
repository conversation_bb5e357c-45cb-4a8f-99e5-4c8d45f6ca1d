import { useState } from "react";
import { useForm } from "react-hook-form";
import axios from "axios";

import { makeSendMarketingEmail, useFormResolver } from "@mio/ui";
import { Email, MarketingEmailDto, MarketingEmailSubjects, UnexpectedError } from "@mio/helpers";
import { environment } from "../../environments/environment";

const sendEmail = async (data: MarketingEmailDto) => {
  const client = axios.create({
    baseURL: environment.BASE_URL,
  });
  const sendMarketingEmail = makeSendMarketingEmail(client);

  return await sendMarketingEmail(data).catch((err) => {
    throw new UnexpectedError(err);
  });
};

export const useSendEmail = (subject: MarketingEmailSubjects) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isError, setIsError] = useState(false);

  const resolver = useFormResolver(Email.toMarketingEmailDto);

  const { register, handleSubmit, formState, reset } = useForm<MarketingEmailDto>({
    resolver,
    defaultValues: {
      subject,
    },
  });

  const formReset = () => {
    reset();
    setIsSuccess(false);
    setIsError(false);
  };

  const onSubmit = handleSubmit(async (data) => {
    setIsLoading(true);
    setIsError(false);

    try {
      await sendEmail(data);
      setIsSuccess(true);
    } catch {
      setIsError(true);
    } finally {
      setIsLoading(false);
    }
  });

  return {
    register,
    onSubmit,
    isLoading,
    isSuccess,
    isError,
    reset: formReset,
    errors: formState.submitCount > 0 ? formState.errors : {},
  };
};
